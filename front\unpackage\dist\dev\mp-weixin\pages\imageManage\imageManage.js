"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_helpers = require("../../utils/helpers.js");
const utils_userManager = require("../../utils/userManager.js");
const utils_dataCache = require("../../utils/dataCache.js");
const utils_performanceMonitor = require("../../utils/performanceMonitor.js");
const utils_urlConfig = require("../../utils/urlConfig.js");
const _sfc_main = {
  data() {
    return {
      orderNumber: "",
      userInfo: {},
      imageList: [],
      selectedImages: [],
      isSelectMode: false,
      isLoading: false,
      isRefreshing: false,
      showPreview: false,
      currentPreviewIndex: 0,
      // 分页相关数据
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      // 活动的计时器列表
      activeTimers: /* @__PURE__ */ new Set()
    };
  },
  computed: {
    currentPreviewImage() {
      return this.imageList[this.currentPreviewIndex];
    },
    /**
     * 是否有图片数据
     */
    hasImages() {
      return this.imageList && Array.isArray(this.imageList) && this.imageList.length > 0;
    },
    /**
     * 是否应该显示空状态
     */
    shouldShowEmpty() {
      return !this.isLoading && !this.hasImages;
    }
  },
  methods: {
    /**
     * 跳转到拍照上传页面
     */
    goToCamera() {
      common_vendor.index.navigateTo({
        url: `/pages/camera/camera?orderNumber=${encodeURIComponent(this.orderNumber)}`,
        success: () => {
          common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:223", "跳转到拍照页面");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:226", "跳转失败:", err);
          utils_helpers.showError("页面跳转失败");
        }
      });
    },
    /**
     * 分页相关方法
     */
    // 上一页
    goToPrevPage() {
      if (this.pagination.hasPrev) {
        this.pagination.page--;
        this.loadImages();
      }
    },
    // 下一页
    goToNextPage() {
      if (this.pagination.hasNext) {
        this.pagination.page++;
        this.loadImages();
      }
    },
    // 跳转到指定页
    goToPage(pageNum) {
      if (pageNum !== this.pagination.page && pageNum >= 1 && pageNum <= this.pagination.totalPages) {
        this.pagination.page = pageNum;
        this.loadImages();
      }
    },
    // 获取页码数组
    getPageNumbers() {
      const { page, totalPages } = this.pagination;
      const numbers = [];
      const start = Math.max(1, page - 2);
      const end = Math.min(totalPages, page + 2);
      for (let i = start; i <= end; i++) {
        numbers.push(i);
      }
      return numbers;
    },
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr)
        return "未知";
      try {
        const date = new Date(timeStr);
        return date.toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit"
        });
      } catch (error) {
        return "未知";
      }
    },
    // 获取图片URL
    getImageUrl(imagePath, imageId) {
      return utils_urlConfig.urlConfig.getImageUrl(imagePath, imageId);
    },
    /**
     * 处理图片点击
     */
    handleImageClick(event) {
      const { image, index } = event;
      if (this.isSelectMode) {
        this.toggleImageSelection(image.id);
      } else {
        this.showImagePreview(index);
      }
    },
    /**
     * 处理图片长按
     */
    handleImageLongPress(image) {
      if (!this.isSelectMode) {
        this.isSelectMode = true;
        this.selectedImages = [image.id];
      }
    },
    /**
     * 切换选择模式
     */
    toggleSelectMode() {
      this.isSelectMode = !this.isSelectMode;
      if (!this.isSelectMode) {
        this.selectedImages = [];
      }
    },
    /**
     * 切换图片选择状态
     */
    toggleImageSelection(imageId) {
      const index = this.selectedImages.indexOf(imageId);
      if (index > -1) {
        this.selectedImages.splice(index, 1);
      } else {
        this.selectedImages.push(imageId);
      }
      if (this.selectedImages.length === 0) {
        this.isSelectMode = false;
      }
    },
    /**
     * 检查图片是否被选中
     */
    isImageSelected(imageId) {
      return this.selectedImages.includes(imageId);
    },
    /**
     * 显示图片预览
     */
    showImagePreview(index) {
      this.currentPreviewIndex = index;
      this.showPreview = true;
    },
    /**
     * 关闭图片预览
     */
    closePreview() {
      this.showPreview = false;
    },
    /**
     * 轮播图切换
     */
    onSwiperChange(e) {
      this.currentPreviewIndex = e.detail.current;
    },
    /**
     * 处理删除选中图片
     */
    async handleDeleteSelected() {
      common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:379", "🗑️ 开始删除操作，选中图片:", this.selectedImages);
      if (this.selectedImages.length === 0) {
        utils_helpers.showError("请先选择要删除的图片");
        return;
      }
      const confirmed = await utils_helpers.showConfirm(
        "确认删除",
        `确定要删除选中的 ${this.selectedImages.length} 张图片吗？此操作不可恢复。`
      );
      common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:391", "🗑️ 用户确认删除:", confirmed);
      if (confirmed) {
        await this.deleteImages();
      }
    },
    /**
     * 删除图片
     */
    async deleteImages() {
      common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:402", "🗑️ 执行删除请求，图片IDs:", this.selectedImages);
      try {
        const response = await this.deleteImagesRequest();
        common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:406", "🗑️ 删除响应:", response);
        if (response.success) {
          utils_helpers.showSuccess(`成功删除 ${this.selectedImages.length} 张图片`);
          this.selectedImages = [];
          this.isSelectMode = false;
          for (let page = 1; page <= this.pagination.totalPages; page++) {
            const cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}_page${page}`;
            utils_dataCache.dataCacheManager.removeCache("images", cacheKey);
          }
          this.pagination.page = 1;
          await this.loadImages(true);
        } else {
          common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:425", "🗑️ 删除失败:", response);
          utils_helpers.showError(response.message || "删除失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:429", "🗑️ 删除图片异常:", error);
        utils_helpers.showError("删除失败：" + error.message);
      }
    },
    /**
     * 删除图片请求
     */
    async deleteImagesRequest() {
      return new Promise((resolve, reject) => {
        const requestConfig = utils_userManager.userManager.createAuthRequest({
          url: "http://localhost:3000/api/images/delete",
          method: "POST",
          data: {
            imageIds: this.selectedImages
          },
          timeout: 15e3,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`服务器错误 (${res.statusCode})`));
            }
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:454", "删除图片请求失败:", err);
            reject(new Error("网络连接失败"));
          }
        });
        common_vendor.index.request(requestConfig);
      });
    },
    /**
     * 加载图片列表（支持分页）
     */
    async loadImages(forceRefresh = false) {
      if (!this.orderNumber || !this.userInfo.factory_name) {
        return;
      }
      const timerName = `loadImages_${this.orderNumber}_page${this.pagination.page}`;
      if (this.activeTimers.has(timerName)) {
        try {
          utils_performanceMonitor.performanceMonitor.endTimer(timerName, "loadTime");
        } catch (error) {
          common_vendor.index.__f__("warn", "at pages/imageManage/imageManage.vue:479", "清理旧计时器失败:", error);
        }
        this.activeTimers.delete(timerName);
      }
      utils_performanceMonitor.performanceMonitor.startTimer(timerName);
      this.activeTimers.add(timerName);
      this.isLoading = true;
      try {
        const cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}_page${this.pagination.page}`;
        const imageData = await utils_dataCache.dataCacheManager.getData(
          "images",
          cacheKey,
          () => this.getImagesRequest(),
          { forceRefresh, ttl: 300 }
          // 5分钟缓存
        );
        if (imageData.success) {
          this.imageList = imageData.data || [];
          if (imageData.pagination) {
            this.pagination = {
              ...this.pagination,
              ...imageData.pagination
            };
          }
          common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:511", `图片列表加载成功: 第${this.pagination.page}页, ${this.imageList.length}/${this.pagination.total} 张图片`);
          const loadDuration = utils_performanceMonitor.performanceMonitor.endTimer(timerName, "loadTime");
          this.activeTimers.delete(timerName);
          utils_performanceMonitor.performanceMonitor.recordLoadPerformance("imageList", this.imageList.length, loadDuration);
          utils_performanceMonitor.performanceMonitor.recordCacheHit("data", !forceRefresh);
        } else {
          common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:521", "获取图片列表失败:", imageData.message);
          this.imageList = [];
          this.pagination.total = 0;
          this.pagination.totalPages = 0;
          utils_performanceMonitor.performanceMonitor.endTimer(timerName, "loadTime");
          this.activeTimers.delete(timerName);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:529", "加载图片列表失败:", error);
        this.imageList = [];
        this.pagination.total = 0;
        this.pagination.totalPages = 0;
        utils_helpers.showError("加载图片列表失败");
        utils_performanceMonitor.performanceMonitor.endTimer(timerName, "loadTime");
        this.activeTimers.delete(timerName);
      } finally {
        this.isLoading = false;
      }
    },
    /**
     * 获取图片列表请求（支持分页）
     */
    async getImagesRequest() {
      return new Promise((resolve, reject) => {
        const requestConfig = utils_userManager.userManager.createAuthRequest({
          url: utils_urlConfig.urlConfig.getApiUrl(`/api/images/order/${encodeURIComponent(this.orderNumber)}?page=${this.pagination.page}&limit=${this.pagination.limit}`),
          method: "GET",
          timeout: 1e4,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`服务器错误 (${res.statusCode})`));
            }
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:558", "获取图片列表请求失败:", err);
            reject(new Error("网络连接失败"));
          }
        });
        common_vendor.index.request(requestConfig);
      });
    },
    /**
     * 下拉刷新
     */
    async handleRefresh() {
      this.isRefreshing = true;
      try {
        await this.loadImages(true);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:576", "刷新失败:", error);
      } finally {
        this.isRefreshing = false;
      }
    },
    /**
     * 获取图片URL - 同步版本，用于模板渲染
     */
    getImageUrl(imagePath, imageId) {
      return utils_urlConfig.urlConfig.getImageUrl(imagePath, imageId);
    },
    /**
     * 处理图片加载错误
     */
    handleImageError(e) {
      var _a;
      common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:595", "图片加载失败:", e);
      common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:596", "失败的图片URL:", ((_a = e.target) == null ? void 0 : _a.src) || "未知");
      common_vendor.index.__f__("error", "at pages/imageManage/imageManage.vue:597", "图片元素:", e.target);
    },
    /**
     * 格式化时间
     */
    formatTime(timeStr) {
      if (!timeStr)
        return "未知";
      try {
        const date = new Date(timeStr);
        return date.toLocaleString();
      } catch (error) {
        return "未知";
      }
    },
    /**
     * 显示性能报告（开发调试用）
     */
    showPerformanceReport() {
      const report = utils_performanceMonitor.performanceMonitor.generateReport();
      common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:619", report);
      {
        common_vendor.index.showModal({
          title: "性能报告",
          content: report,
          showCancel: false
        });
      }
    },
    /**
     * 处理图片上传事件
     */
    handleImageUploaded(data) {
      if (data.orderNumber === this.orderNumber) {
        common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:636", "收到图片上传事件，刷新图片列表");
        const cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}`;
        utils_dataCache.dataCacheManager.removeCache("images", cacheKey);
        this.loadImages(true);
      }
    },
    /**
     * 清理所有活动的计时器
     */
    cleanupActiveTimers() {
      common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:648", "清理活动计时器，数量:", this.activeTimers.size);
      for (const timerName of this.activeTimers) {
        try {
          utils_performanceMonitor.performanceMonitor.endTimer(timerName, "loadTime");
          common_vendor.index.__f__("log", "at pages/imageManage/imageManage.vue:652", "已清理计时器:", timerName);
        } catch (error) {
          common_vendor.index.__f__("warn", "at pages/imageManage/imageManage.vue:654", "清理计时器失败:", timerName, error);
        }
      }
      this.activeTimers.clear();
    },
    /**
     * 初始化
     */
    init() {
      if (utils_userManager.userManager.requireLogin()) {
        this.userInfo = utils_userManager.userManager.getUserInfo();
        this.loadImages();
      }
    }
  },
  onLoad(options) {
    this.orderNumber = options.orderNumber || "";
    if (!this.orderNumber) {
      utils_helpers.showError("订单号不能为空");
      common_vendor.index.navigateBack();
      return;
    }
    this.init();
    common_vendor.index.$on("imageUploaded", this.handleImageUploaded);
  },
  onShow() {
    if (this.orderNumber && this.userInfo.factory_name) {
      for (let page = 1; page <= 10; page++) {
        const cacheKey = `${this.userInfo.factory_name}_${this.orderNumber}_page${page}`;
        utils_dataCache.dataCacheManager.removeCache("images", cacheKey);
      }
      this.pagination.page = 1;
      this.loadImages(true);
    }
  },
  onBackPress() {
    if (this.isSelectMode) {
      this.isSelectMode = false;
      this.selectedImages = [];
      return true;
    }
    this.cleanupActiveTimers();
    return false;
  },
  onUnload() {
    common_vendor.index.$off("imageUploaded", this.handleImageUploaded);
    this.cleanupActiveTimers();
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a;
  return common_vendor.e({
    a: common_vendor.t($data.orderNumber),
    b: common_vendor.t($data.pagination.total || $data.imageList.length),
    c: common_vendor.o((...args) => $options.goToCamera && $options.goToCamera(...args)),
    d: common_vendor.t($data.isSelectMode ? "✓" : "🗑️"),
    e: common_vendor.t($data.isSelectMode ? "取消选择" : "选择删除"),
    f: $data.isSelectMode ? 1 : "",
    g: common_vendor.o((...args) => $options.toggleSelectMode && $options.toggleSelectMode(...args)),
    h: $data.isSelectMode
  }, $data.isSelectMode ? {
    i: common_vendor.t($data.selectedImages.length),
    j: $data.selectedImages.length === 0 ? 1 : "",
    k: $data.selectedImages.length === 0,
    l: common_vendor.o((...args) => $options.handleDeleteSelected && $options.handleDeleteSelected(...args))
  } : {}, {
    m: $data.isSelectMode
  }, $data.isSelectMode ? {} : {}, {
    n: $data.isLoading
  }, $data.isLoading ? {} : $options.hasImages ? {
    p: common_vendor.f($data.imageList, (image, index, i0) => {
      return common_vendor.e($data.isSelectMode ? common_vendor.e({
        a: $data.selectedImages.includes(image.id)
      }, $data.selectedImages.includes(image.id) ? {} : {}, {
        b: $data.selectedImages.includes(image.id) ? 1 : ""
      }) : {}, {
        c: $options.getImageUrl(image.image_path, image.id),
        d: common_vendor.o((...args) => $options.handleImageError && $options.handleImageError(...args), image.id),
        e: common_vendor.t(image.image_name),
        f: common_vendor.t($options.formatTime(image.upload_date)),
        g: image.id,
        h: common_vendor.o(($event) => $options.handleImageClick({
          image,
          index
        }), image.id),
        i: common_vendor.o(($event) => $options.handleImageLongPress(image), image.id)
      });
    }),
    q: $data.isSelectMode
  } : {}, {
    o: $options.hasImages,
    r: $data.pagination.totalPages > 1
  }, $data.pagination.totalPages > 1 ? {
    s: common_vendor.t($data.pagination.page),
    t: common_vendor.t($data.pagination.totalPages),
    v: common_vendor.t($data.pagination.total),
    w: !$data.pagination.hasPrev,
    x: common_vendor.o((...args) => $options.goToPrevPage && $options.goToPrevPage(...args)),
    y: common_vendor.f($options.getPageNumbers(), (pageNum, k0, i0) => {
      return {
        a: common_vendor.t(pageNum),
        b: $data.pagination.page === pageNum ? 1 : "",
        c: pageNum,
        d: common_vendor.o(($event) => $options.goToPage(pageNum), pageNum)
      };
    }),
    z: !$data.pagination.hasNext,
    A: common_vendor.o((...args) => $options.goToNextPage && $options.goToNextPage(...args))
  } : $options.shouldShowEmpty ? {} : {}, {
    B: $options.shouldShowEmpty,
    C: $data.showPreview
  }, $data.showPreview ? {
    D: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args)),
    E: common_vendor.f($data.imageList, (image, k0, i0) => {
      return {
        a: $options.getImageUrl(image.image_path, image.id),
        b: image.id
      };
    }),
    F: $data.currentPreviewIndex,
    G: common_vendor.o((...args) => $options.onSwiperChange && $options.onSwiperChange(...args)),
    H: common_vendor.t((_a = $options.currentPreviewImage) == null ? void 0 : _a.image_name),
    I: common_vendor.t($data.currentPreviewIndex + 1),
    J: common_vendor.t($data.imageList.length),
    K: common_vendor.o(() => {
    }),
    L: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-78a14e75"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/imageManage/imageManage.js.map
