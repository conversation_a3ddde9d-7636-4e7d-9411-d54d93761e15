# 小程序后端HTTPS部署指南

## 🚀 部署概述

本指南将帮助您在服务器 `************` 上部署小程序后端，使用域名 `www.mls2005.top` 和HTTPS协议。

## 📋 部署前检查清单

### 1. SSL证书
确保以下SSL证书文件存在：
- `C:/MLS/Warehouse/server/ssl/www.mls2005.top.pem`
- `C:/MLS/Warehouse/server/ssl/www.mls2005.top.key`

### 2. 目录结构
确保以下目录存在：
- `C:/MLS/Warehouse_Img/` - 仓库图片存储
- `C:/MLS/Order_Img/` - 订单图片存储
- `C:/Warehouse_Img/temp_uploads/` - 临时上传目录

### 3. 数据库连接
确保MySQL数据库可访问：
- 主机: ************
- 用户: mls01
- 数据库: identify

## 🔧 部署步骤

### 1. 停止现有服务
```bash
# 停止nginx
nginx -s stop

# 停止Node.js应用（如果正在运行）
pkill -f "node.*app.js"
```

### 2. 更新代码
将修改后的代码文件上传到服务器：
- `server/app.js`
- `server/.env`
- `nginx.conf`
- `front/utils/apiService.js`
- `front/manifest.json`

### 3. 安装依赖
```bash
cd /path/to/server
npm install
```

### 4. 启动Node.js应用
```bash
cd /path/to/server
npm start
# 或者使用PM2
pm2 start app.js --name "warehouse-api"
```

### 5. 启动Nginx
```bash
# 测试nginx配置
nginx -t

# 启动nginx
nginx
```

## 🌐 访问地址

### 生产环境
- **主域名**: https://www.mls2005.top
- **IP访问**: https://************
- **API接口**: https://www.mls2005.top/api/
- **静态文件**: https://www.mls2005.top/warehouse-images/

### 开发环境
- **本地开发**: http://localhost:3000

## 🔍 验证部署

### 1. 检查服务状态
```bash
# 检查Node.js进程
ps aux | grep node

# 检查nginx进程
ps aux | grep nginx

# 检查端口占用
netstat -tlnp | grep :3000
netstat -tlnp | grep :443
```

### 2. 测试API接口
```bash
# 测试健康检查
curl https://www.mls2005.top/api/health

# 测试CORS
curl -H "Origin: https://www.mls2005.top" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     https://www.mls2005.top/api/health
```

### 3. 检查SSL证书
```bash
# 检查证书有效期
openssl x509 -in C:/MLS/Warehouse/server/ssl/www.mls2005.top.pem -text -noout

# 在线测试SSL
# 访问: https://www.ssllabs.com/ssltest/
```

## 🛠️ 故障排除

### 1. 常见问题

#### SSL证书错误
- 检查证书文件路径是否正确
- 确认证书未过期
- 验证证书与域名匹配

#### CORS错误
- 检查nginx配置中的CORS头设置
- 确认前端请求的域名正确

#### 数据库连接失败
- 检查数据库服务状态
- 验证连接参数
- 检查防火墙设置

### 2. 日志查看
```bash
# Node.js应用日志
tail -f /path/to/server/logs/application.log

# Nginx访问日志
tail -f /var/log/nginx/access.log

# Nginx错误日志
tail -f /var/log/nginx/error.log
```

## 📱 小程序配置

### 1. 服务器域名配置
在微信小程序后台配置以下域名：
- **request合法域名**: `https://www.mls2005.top`
- **uploadFile合法域名**: `https://www.mls2005.top`
- **downloadFile合法域名**: `https://www.mls2005.top`

### 2. 业务域名配置
如果需要webview功能，添加：
- **业务域名**: `www.mls2005.top`

## 🔒 安全配置

### 1. 防火墙设置
确保以下端口开放：
- 80 (HTTP重定向)
- 443 (HTTPS)
- 3000 (内部Node.js，仅本地访问)

### 2. SSL安全头
nginx配置已包含以下安全头：
- Strict-Transport-Security
- X-Frame-Options
- X-Content-Type-Options
- X-XSS-Protection
- Referrer-Policy

## 📊 监控和维护

### 1. 性能监控
- 使用 `/api/db-monitor` 监控数据库状态
- 使用 `/api/health` 检查服务健康状态

### 2. 定期维护
- 定期更新SSL证书
- 清理临时文件和日志
- 监控磁盘空间使用

## 🆘 紧急联系

如遇到部署问题，请检查：
1. 服务器资源使用情况
2. 网络连接状态
3. 证书有效性
4. 数据库连接

---

**部署完成后，请务必进行完整的功能测试！**
