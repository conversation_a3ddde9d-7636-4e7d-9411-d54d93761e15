{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "E:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<template>\n\t<view class=\"login-container\">\n\t\t<!-- 头部区域 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"logo-section\">\n\t\t\t\t<text class=\"logo-icon\">🏭</text>\n\t\t\t\t<text class=\"logo-text\">码单图片上传系统</text>\n\t\t\t</view>\n\t\t\t<text class=\"subtitle\">请登录您的工厂账户</text>\n\t\t</view>\n\n\t\t<!-- 登录表单 -->\n\t\t<view class=\"form-container\">\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<view class=\"input-label\">\n\t\t\t\t\t<text class=\"label-icon\">👤</text>\n\t\t\t\t\t<text class=\"label-text\">用户名</text>\n\t\t\t\t</view>\n\t\t\t\t<input \n\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\ttype=\"text\" \n\t\t\t\t\tv-model=\"loginForm.username\"\n\t\t\t\t\tplaceholder=\"请输入用户名\"\n\t\t\t\t\t:disabled=\"isLoading\"\n\t\t\t\t/>\n\t\t\t</view>\n\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<view class=\"input-label\">\n\t\t\t\t\t<text class=\"label-icon\">🔒</text>\n\t\t\t\t\t<text class=\"label-text\">密码</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"password-input-container\">\n\t\t\t\t\t<input\n\t\t\t\t\t\tclass=\"form-input password-input\"\n\t\t\t\t\t\t:type=\"passwordInputType\"\n\t\t\t\t\t\t:password=\"!showPassword\"\n\t\t\t\t\t\tv-model=\"loginForm.password\"\n\t\t\t\t\t\tplaceholder=\"请输入密码\"\n\t\t\t\t\t\t:disabled=\"isLoading\"\n\t\t\t\t\t\t@confirm=\"handleLogin\"\n\t\t\t\t\t/>\n\t\t\t\t\t<view class=\"password-toggle\" @click=\"togglePassword\">\n\t\t\t\t\t\t<text class=\"toggle-icon\" :class=\"{'active': showPassword}\">{{ passwordToggleIcon }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 登录按钮 -->\n\t\t\t<button \n\t\t\t\tclass=\"login-btn\" \n\t\t\t\t:class=\"{ 'loading': isLoading }\"\n\t\t\t\t:disabled=\"isLoading || !canSubmit\"\n\t\t\t\t@click=\"handleLogin\"\n\t\t\t>\n\t\t\t\t<text v-if=\"isLoading\" class=\"loading-text\">登录中...</text>\n\t\t\t\t<text v-else>登录</text>\n\t\t\t</button>\n\t\t</view>\n\n\t\t<!-- 底部信息 -->\n\t\t<view class=\"footer\">\n\t\t\t<text class=\"footer-text\">如有账户问题，请联系系统管理员</text>\n\t\t</view>\n\n\t\t<!-- 加载遮罩 -->\n\t\t<view class=\"loading-overlay\" v-if=\"isLoading\">\n\t\t\t<view class=\"loading-content\">\n\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t<text class=\"loading-message\">正在验证登录信息...</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { showSuccess, showError } from '../../utils/helpers.js';\n\timport userManager from '../../utils/userManager.js';\n\timport urlConfig from '../../utils/urlConfig.js';\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tloginForm: {\n\t\t\t\t\tusername: '',\n\t\t\t\t\tpassword: ''\n\t\t\t\t},\n\t\t\t\tisLoading: false,\n\t\t\t\tshowPassword: false\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tcanSubmit() {\n\t\t\t\treturn this.loginForm.username.trim() && this.loginForm.password.trim();\n\t\t\t},\n\t\t\tpasswordInputType() {\n\t\t\t\treturn this.showPassword ? 'text' : 'password';\n\t\t\t},\n\t\t\tpasswordToggleIcon() {\n\t\t\t\treturn this.showPassword ? '🙈' : '👁️';\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t/**\n\t\t\t * 切换密码显示状态\n\t\t\t */\n\t\t\ttogglePassword() {\n\t\t\t\tconsole.log('切换密码显示状态，当前状态:', this.showPassword);\n\t\t\t\tthis.showPassword = !this.showPassword;\n\t\t\t\tconsole.log('切换后状态:', this.showPassword);\n\t\t\t\t// 强制更新视图\n\t\t\t\tthis.$forceUpdate();\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 处理登录\n\t\t\t */\n\t\t\tasync handleLogin() {\n\t\t\t\tif (!this.canSubmit) {\n\t\t\t\t\tshowError('请输入用户名和密码');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tthis.isLoading = true;\n\n\t\t\t\ttry {\n\t\t\t\t\t// 调用登录API\n\t\t\t\t\tconst response = await this.loginRequest();\n\t\t\t\t\t\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\t// 使用用户管理器保存登录信息\n\t\t\t\t\t\tconst loginSuccess = userManager.login(response.data);\n\n\t\t\t\t\t\tif (loginSuccess) {\n\t\t\t\t\t\t\tshowSuccess('登录成功');\n\n\t\t\t\t\t\t\t// 跳转到订单管理页面\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\t\t\turl: '/pages/order/order'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tshowError('保存登录信息失败');\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tshowError(response.message || '登录失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('登录失败:', error);\n\t\t\t\t\tshowError(error.message || '登录失败，请检查网络连接');\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 登录请求\n\t\t\t */\n\t\t\tasync loginRequest() {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tuni.request({\n\t\t\t\t\t\turl: urlConfig.getApiUrl('/api/auth/login'),\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tusername: this.loginForm.username.trim(),\n\t\t\t\t\t\t\tpassword: this.loginForm.password.trim()\n\t\t\t\t\t\t},\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t\t},\n\t\t\t\t\t\ttimeout: 10000,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t// 无论状态码如何，都返回响应数据\n\t\t\t\t\t\t\t// 让调用方根据 success 字段判断是否成功\n\t\t\t\t\t\t\tresolve(res.data);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('登录请求失败:', err);\n\t\t\t\t\t\t\treject(new Error('网络连接失败'));\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\n\n\t\t},\n\n\t\tonLoad() {\n\t\t\t// 检查是否已经登录\n\t\t\tif (userManager.checkLoginStatus()) {\n\t\t\t\t// 已登录，直接跳转到订单管理页面\n\t\t\t\tuni.redirectTo({\n\t\t\t\t\turl: '/pages/order/order'\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.login-container {\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tpadding: 40rpx 60rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t}\n\n\t/* 头部样式 */\n\t.header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 80rpx;\n\t}\n\n\t.logo-section {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.logo-icon {\n\t\tfont-size: 80rpx;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.logo-text {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tcolor: white;\n\t}\n\n\t.subtitle {\n\t\tfont-size: 28rpx;\n\t\tcolor: rgba(255, 255, 255, 0.8);\n\t}\n\n\t/* 表单样式 */\n\t.form-container {\n\t\tbackground: rgba(255, 255, 255, 0.95);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 50rpx 40rpx;\n\t\tbox-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);\n\t\tbackdrop-filter: blur(10rpx);\n\t\tbox-sizing: border-box;\n\t\tmax-width: 100%;\n\t\toverflow: hidden;\n\t\tflex-shrink: 0;\n\t\tmax-height: 70vh;\n\t}\n\n\t.form-item {\n\t\tmargin-bottom: 35rpx;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.input-label {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 15rpx;\n\t}\n\n\t.label-icon {\n\t\tfont-size: 32rpx;\n\t\tmargin-right: 15rpx;\n\t\tcolor: #667eea;\n\t}\n\n\t.label-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #333333;\n\t\tfont-weight: 500;\n\t}\n\n\t.form-input {\n\t\twidth: 100%;\n\t\theight: 90rpx;\n\t\tborder: 2rpx solid #e0e0e0;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 0 30rpx;\n\t\tfont-size: 32rpx;\n\t\tcolor: #333333;\n\t\tbackground-color: #ffffff;\n\t\ttransition: all 0.3s ease;\n\t\tbox-sizing: border-box;\n\t}\n\n\t/* 密码输入框容器 */\n\t.password-input-container {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t}\n\n\t.password-input {\n\t\tpadding-right: 80rpx !important;\n\t}\n\n\t/* 密码显示/隐藏按钮 */\n\t.password-toggle {\n\t\tposition: absolute;\n\t\tright: 20rpx;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 50rpx;\n\t\theight: 50rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcursor: pointer;\n\t\tz-index: 10;\n\t}\n\n\t.toggle-icon {\n\t\tfont-size: 32rpx;\n\t\tcolor: #999999;\n\t\ttransition: color 0.3s ease;\n\t}\n\n\t.toggle-icon.active {\n\t\tcolor: #667eea;\n\t}\n\n\t.password-toggle:active .toggle-icon {\n\t\tcolor: #667eea;\n\t}\n\n\t.form-input:focus {\n\t\tborder-color: #667eea;\n\t\tbox-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);\n\t}\n\n\t.form-input:disabled {\n\t\tbackground-color: #f5f5f5;\n\t\tcolor: #999999;\n\t}\n\n\t.login-btn {\n\t\twidth: 100%;\n\t\theight: 90rpx;\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\t\tborder: none;\n\t\tborder-radius: 12rpx;\n\t\tcolor: #ffffff;\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tmargin-top: 40rpx;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.login-btn:not(:disabled):active {\n\t\ttransform: scale(0.98);\n\t}\n\n\t.login-btn:disabled {\n\t\topacity: 0.6;\n\t\ttransform: none;\n\t}\n\n\t.login-btn.loading {\n\t\tbackground: #cccccc;\n\t}\n\n\t.loading-text {\n\t\tcolor: #666666;\n\t}\n\n\t/* 底部 */\n\t.footer {\n\t\ttext-align: center;\n\t\tmargin-top: auto;\n\t}\n\n\t.footer-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: rgba(255, 255, 255, 0.7);\n\t}\n\n\t.loading-overlay {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 9999;\n\t}\n\n\t.loading-content {\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 60rpx 40rpx;\n\t\ttext-align: center;\n\t\tmin-width: 300rpx;\n\t}\n\n\t.loading-spinner {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tborder: 4rpx solid #f3f3f3;\n\t\tborder-top: 4rpx solid #667eea;\n\t\tborder-radius: 50%;\n\t\tanimation: spin 1s linear infinite;\n\t\tmargin: 0 auto 30rpx;\n\t}\n\n\t.loading-message {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t}\n\n\t@keyframes spin {\n\t\t0% { transform: rotate(0deg); }\n\t\t100% { transform: rotate(360deg); }\n\t}\n\n\t/* 响应式设计 */\n\t@media screen and (max-width: 750rpx) {\n\t\t.login-container {\n\t\t\tpadding: 40rpx 20rpx;\n\t\t}\n\n\t\t.logo-text {\n\t\t\tfont-size: 42rpx;\n\t\t}\n\n\t\t.form-container {\n\t\t\tpadding: 50rpx 30rpx;\n\t\t\tmargin: 0 10rpx;\n\t\t}\n\n\t\t.form-input {\n\t\t\tpadding: 0 25rpx;\n\t\t}\n\n\t\t.password-input {\n\t\t\tpadding-right: 70rpx !important;\n\t\t}\n\n\t\t.password-toggle {\n\t\t\tright: 15rpx;\n\t\t\twidth: 45rpx;\n\t\t\theight: 45rpx;\n\t\t}\n\n\t\t.toggle-icon {\n\t\t\tfont-size: 28rpx;\n\t\t}\n\t}\n</style>\n", "import MiniProgramPage from 'D:/Desktop/Warehouse/front/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "showError", "userManager", "showSuccess", "urlConfig"], "mappings": ";;;;;AAgFC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,MACV;AAAA,MACD,WAAW;AAAA,MACX,cAAc;AAAA,IACf;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,YAAY;AACX,aAAO,KAAK,UAAU,SAAS,KAAI,KAAM,KAAK,UAAU,SAAS;IACjE;AAAA,IACD,oBAAoB;AACnB,aAAO,KAAK,eAAe,SAAS;AAAA,IACpC;AAAA,IACD,qBAAqB;AACpB,aAAO,KAAK,eAAe,OAAO;AAAA,IACnC;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAIR,iBAAiB;AAChBA,uEAAY,kBAAkB,KAAK,YAAY;AAC/C,WAAK,eAAe,CAAC,KAAK;AAC1BA,oBAAA,MAAA,MAAA,OAAA,gCAAY,UAAU,KAAK,YAAY;AAEvC,WAAK,aAAY;AAAA,IACjB;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,cAAc;AACnB,UAAI,CAAC,KAAK,WAAW;AACpBC,sBAAS,UAAC,WAAW;AACrB;AAAA,MACD;AAEA,WAAK,YAAY;AAEjB,UAAI;AAEH,cAAM,WAAW,MAAM,KAAK;AAE5B,YAAI,SAAS,SAAS;AAErB,gBAAM,eAAeC,kBAAW,YAAC,MAAM,SAAS,IAAI;AAEpD,cAAI,cAAc;AACjBC,0BAAW,YAAC,MAAM;AAGlB,uBAAW,MAAM;AAChBH,4BAAAA,MAAI,WAAW;AAAA,gBACd,KAAK;AAAA,cACN,CAAC;AAAA,YACD,GAAE,GAAI;AAAA,iBACD;AACNC,0BAAS,UAAC,UAAU;AAAA,UACrB;AAAA,eACM;AACNA,wBAAAA,UAAU,SAAS,WAAW,MAAM;AAAA,QACrC;AAAA,MACC,SAAO,OAAO;AACfD,2EAAc,SAAS,KAAK;AAC5BC,sBAAAA,UAAU,MAAM,WAAW,cAAc;AAAA,MAC1C,UAAU;AACT,aAAK,YAAY;AAAA,MAClB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,eAAe;AACpB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCD,sBAAAA,MAAI,QAAQ;AAAA,UACX,KAAKI,gBAAAA,UAAU,UAAU,iBAAiB;AAAA,UAC1C,QAAQ;AAAA,UACR,MAAM;AAAA,YACL,UAAU,KAAK,UAAU,SAAS,KAAM;AAAA,YACxC,UAAU,KAAK,UAAU,SAAS,KAAK;AAAA,UACvC;AAAA,UACD,QAAQ;AAAA,YACP,gBAAgB;AAAA,UAChB;AAAA,UACD,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AAGjB,oBAAQ,IAAI,IAAI;AAAA,UAChB;AAAA,UACD,MAAM,CAAC,QAAQ;AACdJ,0BAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,GAAG;AAC5B,mBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,UAC3B;AAAA,QACD,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA,EAGD;AAAA,EAED,SAAS;AAER,QAAIE,kBAAAA,YAAY,oBAAoB;AAEnCF,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;ACpMD,GAAG,WAAW,eAAe;"}