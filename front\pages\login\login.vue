<template>
	<view class="login-container">
		<!-- 头部区域 -->
		<view class="header">
			<view class="logo-section">
				<text class="logo-icon">🏭</text>
				<text class="logo-text">码单图片上传系统</text>
			</view>
			<text class="subtitle">请登录您的工厂账户</text>
		</view>

		<!-- 登录表单 -->
		<view class="form-container">
			<view class="form-item">
				<view class="input-label">
					<text class="label-icon">👤</text>
					<text class="label-text">用户名</text>
				</view>
				<input 
					class="form-input" 
					type="text" 
					v-model="loginForm.username"
					placeholder="请输入用户名"
					:disabled="isLoading"
				/>
			</view>

			<view class="form-item">
				<view class="input-label">
					<text class="label-icon">🔒</text>
					<text class="label-text">密码</text>
				</view>
				<view class="password-input-container">
					<input
						class="form-input password-input"
						:type="passwordInputType"
						:password="!showPassword"
						v-model="loginForm.password"
						placeholder="请输入密码"
						:disabled="isLoading"
						@confirm="handleLogin"
					/>
					<view class="password-toggle" @click="togglePassword">
						<text class="toggle-icon" :class="{'active': showPassword}">{{ passwordToggleIcon }}</text>
					</view>
				</view>
			</view>

			<!-- 登录按钮 -->
			<button 
				class="login-btn" 
				:class="{ 'loading': isLoading }"
				:disabled="isLoading || !canSubmit"
				@click="handleLogin"
			>
				<text v-if="isLoading" class="loading-text">登录中...</text>
				<text v-else>登录</text>
			</button>
		</view>

		<!-- 底部信息 -->
		<view class="footer">
			<text class="footer-text">如有账户问题，请联系系统管理员</text>
		</view>

		<!-- 加载遮罩 -->
		<view class="loading-overlay" v-if="isLoading">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-message">正在验证登录信息...</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { showSuccess, showError } from '../../utils/helpers.js';
	import userManager from '../../utils/userManager.js';
	import urlConfig from '../../utils/urlConfig.js';

	export default {
		data() {
			return {
				loginForm: {
					username: '',
					password: ''
				},
				isLoading: false,
				showPassword: false
			}
		},
		computed: {
			canSubmit() {
				return this.loginForm.username.trim() && this.loginForm.password.trim();
			},
			passwordInputType() {
				return this.showPassword ? 'text' : 'password';
			},
			passwordToggleIcon() {
				return this.showPassword ? '🙈' : '👁️';
			}
		},
		methods: {
			/**
			 * 切换密码显示状态
			 */
			togglePassword() {
				console.log('切换密码显示状态，当前状态:', this.showPassword);
				this.showPassword = !this.showPassword;
				console.log('切换后状态:', this.showPassword);
				// 强制更新视图
				this.$forceUpdate();
			},

			/**
			 * 处理登录
			 */
			async handleLogin() {
				if (!this.canSubmit) {
					showError('请输入用户名和密码');
					return;
				}

				this.isLoading = true;

				try {
					// 调用登录API
					const response = await this.loginRequest();
					
					if (response.success) {
						// 使用用户管理器保存登录信息
						const loginSuccess = userManager.login(response.data);

						if (loginSuccess) {
							showSuccess('登录成功');

							// 跳转到订单管理页面
							setTimeout(() => {
								uni.redirectTo({
									url: '/pages/order/order'
								});
							}, 1000);
						} else {
							showError('保存登录信息失败');
						}
					} else {
						showError(response.message || '登录失败');
					}
				} catch (error) {
					console.error('登录失败:', error);
					showError(error.message || '登录失败，请检查网络连接');
				} finally {
					this.isLoading = false;
				}
			},

			/**
			 * 登录请求
			 */
			async loginRequest() {
				return new Promise((resolve, reject) => {
					uni.request({
						url: urlConfig.getApiUrl('/api/auth/login'),
						method: 'POST',
						data: {
							username: this.loginForm.username.trim(),
							password: this.loginForm.password.trim()
						},
						header: {
							'Content-Type': 'application/json'
						},
						timeout: 10000,
						success: (res) => {
							// 无论状态码如何，都返回响应数据
							// 让调用方根据 success 字段判断是否成功
							resolve(res.data);
						},
						fail: (err) => {
							console.error('登录请求失败:', err);
							reject(new Error('网络连接失败'));
						}
					});
				});
			},


		},

		onLoad() {
			// 检查是否已经登录
			if (userManager.checkLoginStatus()) {
				// 已登录，直接跳转到订单管理页面
				uni.redirectTo({
					url: '/pages/order/order'
				});
			}
		}
	}
</script>

<style scoped>
	.login-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 40rpx 60rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	/* 头部样式 */
	.header {
		text-align: center;
		margin-bottom: 80rpx;
	}

	.logo-section {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
	}

	.logo-icon {
		font-size: 80rpx;
		margin-right: 20rpx;
	}

	.logo-text {
		font-size: 48rpx;
		font-weight: bold;
		color: white;
	}

	.subtitle {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	/* 表单样式 */
	.form-container {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 20rpx;
		padding: 50rpx 40rpx;
		box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
		backdrop-filter: blur(10rpx);
		box-sizing: border-box;
		max-width: 100%;
		overflow: hidden;
		flex-shrink: 0;
		max-height: 70vh;
	}

	.form-item {
		margin-bottom: 35rpx;
		width: 100%;
		box-sizing: border-box;
	}

	.input-label {
		display: flex;
		align-items: center;
		margin-bottom: 15rpx;
	}

	.label-icon {
		font-size: 32rpx;
		margin-right: 15rpx;
		color: #667eea;
	}

	.label-text {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
	}

	.form-input {
		width: 100%;
		height: 90rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 12rpx;
		padding: 0 30rpx;
		font-size: 32rpx;
		color: #333333;
		background-color: #ffffff;
		transition: all 0.3s ease;
		box-sizing: border-box;
	}

	/* 密码输入框容器 */
	.password-input-container {
		position: relative;
		width: 100%;
	}

	.password-input {
		padding-right: 80rpx !important;
	}

	/* 密码显示/隐藏按钮 */
	.password-toggle {
		position: absolute;
		right: 20rpx;
		top: 50%;
		transform: translateY(-50%);
		width: 50rpx;
		height: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		z-index: 10;
	}

	.toggle-icon {
		font-size: 32rpx;
		color: #999999;
		transition: color 0.3s ease;
	}

	.toggle-icon.active {
		color: #667eea;
	}

	.password-toggle:active .toggle-icon {
		color: #667eea;
	}

	.form-input:focus {
		border-color: #667eea;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}

	.form-input:disabled {
		background-color: #f5f5f5;
		color: #999999;
	}

	.login-btn {
		width: 100%;
		height: 90rpx;
		background: linear-gradient(135deg, #667eea, #764ba2);
		border: none;
		border-radius: 12rpx;
		color: #ffffff;
		font-size: 36rpx;
		font-weight: bold;
		margin-top: 40rpx;
		transition: all 0.3s ease;
	}

	.login-btn:not(:disabled):active {
		transform: scale(0.98);
	}

	.login-btn:disabled {
		opacity: 0.6;
		transform: none;
	}

	.login-btn.loading {
		background: #cccccc;
	}

	.loading-text {
		color: #666666;
	}

	/* 底部 */
	.footer {
		text-align: center;
		margin-top: auto;
	}

	.footer-text {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.7);
	}

	.loading-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}

	.loading-content {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 60rpx 40rpx;
		text-align: center;
		min-width: 300rpx;
	}

	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #667eea;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin: 0 auto 30rpx;
	}

	.loading-message {
		font-size: 28rpx;
		color: #666666;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	/* 响应式设计 */
	@media screen and (max-width: 750rpx) {
		.login-container {
			padding: 40rpx 20rpx;
		}

		.logo-text {
			font-size: 42rpx;
		}

		.form-container {
			padding: 50rpx 30rpx;
			margin: 0 10rpx;
		}

		.form-input {
			padding: 0 25rpx;
		}

		.password-input {
			padding-right: 70rpx !important;
		}

		.password-toggle {
			right: 15rpx;
			width: 45rpx;
			height: 45rpx;
		}

		.toggle-icon {
			font-size: 28rpx;
		}
	}
</style>
