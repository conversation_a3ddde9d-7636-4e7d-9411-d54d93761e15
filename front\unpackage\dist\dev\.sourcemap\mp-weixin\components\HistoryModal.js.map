{"version": 3, "file": "HistoryModal.js", "sources": ["components/HistoryModal.vue", "E:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovRGVza3RvcC9XYXJlaG91c2UvZnJvbnQvY29tcG9uZW50cy9IaXN0b3J5TW9kYWwudnVl"], "sourcesContent": ["<template>\n\t<view class=\"history-modal\" v-if=\"visible\">\n\t\t<!-- 遮罩层 -->\n\t\t<view class=\"modal-overlay\" @click=\"closeModal\"></view>\n\t\t\n\t\t<!-- 弹窗内容 -->\n\t\t<view class=\"modal-content\">\n\t\t\t<!-- 标题栏 -->\n\t\t\t<view class=\"modal-header\">\n\t\t\t\t<text class=\"modal-title\">📋 今日历史记录</text>\n\t\t\t\t<view class=\"close-btn\" @click=\"closeModal\">\n\t\t\t\t\t<text class=\"close-icon\">×</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 日期信息 -->\n\t\t\t<view class=\"date-info\">\n\t\t\t\t<text class=\"date-text\">{{currentDate}}</text>\n\t\t\t\t<text class=\"count-text\">共 {{records.length}} 条记录</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 加载状态 -->\n\t\t\t<view class=\"loading-container\" v-if=\"loading\">\n\t\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t\t<text class=\"loading-text\">正在加载历史记录...</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 空状态 -->\n\t\t\t<view class=\"empty-state\" v-else-if=\"records.length === 0\">\n\t\t\t\t<text class=\"empty-icon\">📂</text>\n\t\t\t\t<text class=\"empty-text\">今日暂无上传记录</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 记录列表 -->\n\t\t\t<view class=\"records-wrapper\" v-else>\n\t\t\t\t<!-- 横向滚动容器 -->\n\t\t\t\t<scroll-view class=\"records-horizontal-scroll\" scroll-x=\"true\" scroll-y=\"false\">\n\t\t\t\t\t<!-- 竖向滚动容器 -->\n\t\t\t\t\t<scroll-view class=\"records-vertical-scroll\" scroll-y=\"true\" scroll-x=\"false\">\n\t\t\t\t\t\t<view class=\"records-content\">\n\t\t\t\t\t\t\t<view class=\"record-item\" v-for=\"(record, index) in records\" :key=\"index\">\n\t\t\t\t\t\t\t\t<!-- 记录信息 -->\n\t\t\t\t\t\t\t\t<view class=\"record-info\">\n\t\t\t\t\t\t\t\t\t<view class=\"record-header\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"record-title\">{{record.ImageName}}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"record-time\">{{record.UploadTime}}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t\t<view class=\"record-details\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-label\">类型:</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-value category\" :class=\"getCategoryClass(record.recognitionType)\">\n\t\t\t\t\t\t\t\t\t\t\t\t{{record.category}}\n\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t\t\t<view class=\"detail-item\" v-if=\"record.CartonNumber\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-label\">识别结果:</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-value result-text\">{{record.CartonNumber}}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-label\">文件大小:</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-value\">{{formatFileSize(record.fileSize)}}</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t<!-- 图片预览 -->\n\t\t\t\t\t\t\t\t<view class=\"record-image\" v-if=\"record.hasImage !== false\" @click=\"previewImage(record)\">\n\t\t\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\t\t\t:src=\"getImageUrl(record.ImagePath)\"\n\t\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\t\t\t\tclass=\"preview-img\"\n\t\t\t\t\t\t\t\t\t\t@error=\"onImageError(record, index)\"\n\t\t\t\t\t\t\t\t\t\t@load=\"onImageLoad(record, index)\"\n\t\t\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t\t\t\t<view class=\"preview-overlay\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"preview-text\">点击预览</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t<!-- 无图片状态 -->\n\t\t\t\t\t\t\t\t<view class=\"no-image\" v-else>\n\t\t\t\t\t\t\t\t\t<text class=\"no-image-icon\">🖼️</text>\n\t\t\t\t\t\t\t\t\t<text class=\"no-image-text\">图片不可用</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 底部操作 -->\n\t\t\t<view class=\"modal-footer\">\n\t\t\t\t<button class=\"refresh-btn\" @click=\"loadTodayHistory\">\n\t\t\t\t\t<text class=\"btn-icon\">🔄</text>\n\t\t\t\t\t<text class=\"btn-text\">刷新</text>\n\t\t\t\t</button>\n\t\t\t\t<button class=\"close-btn-footer\" @click=\"closeModal\">\n\t\t\t\t\t<text class=\"btn-text\">关闭</text>\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport apiService from '@/utils/apiService.js';\n\timport { showError, showSuccess } from '@/utils/helpers.js';\n\timport warehouseUserManager from '@/utils/warehouseUsers.js';\n\timport urlConfig from '@/utils/urlConfig.js';\n\n\texport default {\n\t\tname: 'HistoryModal',\n\t\tprops: {\n\t\t\tvisible: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tloading: false,\n\t\t\t\trecords: [],\n\t\t\t\tcurrentDate: ''\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tvisible(newVal) {\n\t\t\t\tif (newVal) {\n\t\t\t\t\tthis.loadTodayHistory();\n\t\t\t\t\tthis.updateCurrentDate();\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t/**\n\t\t\t * 加载今日历史记录\n\t\t\t */\n\t\t\tasync loadTodayHistory() {\n\t\t\t\tthis.loading = true;\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('📋 开始加载今日历史记录...');\n\n\t\t\t\t\t// 获取当前登录用户的用户名\n\t\t\t\t\tconst currentUser = warehouseUserManager.getCurrentUser();\n\t\t\t\t\tif (!currentUser) {\n\t\t\t\t\t\tshowError('请先登录');\n\t\t\t\t\t\tthis.records = [];\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tconst username = currentUser.username;\n\t\t\t\t\tconsole.log(`📋 查询用户 ${username} 的历史记录`);\n\n\t\t\t\t\tconst result = await apiService.getTodayHistory(username);\n\t\t\t\t\tthis.records = result.data || [];\n\t\t\t\t\tconsole.log(`✅ 加载完成，共 ${this.records.length} 条记录`);\n\n\t\t\t\t\tif (this.records.length > 0) {\n\t\t\t\t\t\tshowSuccess(`加载了 ${this.records.length} 条今日记录`);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tshowSuccess('今日暂无上传记录');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 加载历史记录失败:', error);\n\t\t\t\t\tshowError('加载历史记录失败: ' + error.message);\n\t\t\t\t\tthis.records = [];\n\t\t\t\t} finally {\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 更新当前日期显示\n\t\t\t */\n\t\t\tupdateCurrentDate() {\n\t\t\t\tconst now = new Date();\n\t\t\t\tconst year = now.getFullYear();\n\t\t\t\tconst month = String(now.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(now.getDate()).padStart(2, '0');\n\t\t\t\tconst weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];\n\t\t\t\tconst weekday = weekdays[now.getDay()];\n\t\t\t\t\n\t\t\t\tthis.currentDate = `${year}年${month}月${day}日 ${weekday}`;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 获取分类样式类名\n\t\t\t */\n\t\t\tgetCategoryClass(type) {\n\t\t\t\tconst classMap = {\n\t\t\t\t\t'general': 'category-general',\n\t\t\t\t\t'registration': 'category-registration', \n\t\t\t\t\t'carton_info': 'category-carton',\n\t\t\t\t\t'unknown': 'category-unknown'\n\t\t\t\t};\n\t\t\t\treturn classMap[type] || 'category-general';\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 格式化文件大小\n\t\t\t */\n\t\t\tformatFileSize(bytes) {\n\t\t\t\tif (!bytes || bytes === 0) return '未知';\n\t\t\t\t\n\t\t\t\tconst units = ['B', 'KB', 'MB', 'GB'];\n\t\t\t\tlet size = bytes;\n\t\t\t\tlet unitIndex = 0;\n\t\t\t\t\n\t\t\t\twhile (size >= 1024 && unitIndex < units.length - 1) {\n\t\t\t\t\tsize /= 1024;\n\t\t\t\t\tunitIndex++;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn `${size.toFixed(1)} ${units[unitIndex]}`;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 获取图片URL\n\t\t\t */\n\t\t\tgetImageUrl(imagePath) {\n\t\t\t\treturn urlConfig.getHistoryImageUrl(imagePath);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 预览图片\n\t\t\t */\n\t\t\tpreviewImage(record) {\n\t\t\t\tconsole.log('🖼️ 预览图片:', record.ImageName);\n\t\t\t\t\n\t\t\t\tconst imageUrl = this.getImageUrl(record.ImagePath);\n\t\t\t\t\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: [imageUrl],\n\t\t\t\t\tcurrent: imageUrl,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconsole.log('✅ 图片预览成功');\n\t\t\t\t\t},\n\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\tconsole.error('❌ 图片预览失败:', error);\n\t\t\t\t\t\tshowError('图片预览失败');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 图片加载错误处理\n\t\t\t */\n\t\t\tonImageError(record, index) {\n\t\t\t\tconsole.error('❌ 图片加载失败:', record.ImagePath);\n\t\t\t\t// 标记图片不可用\n\t\t\t\tthis.records[index].hasImage = false;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 图片加载成功处理\n\t\t\t */\n\t\t\tonImageLoad(record, index) {\n\t\t\t\tconsole.log('✅ 图片加载成功:', record.ImagePath);\n\t\t\t\t// 确保图片标记为可用\n\t\t\t\tthis.records[index].hasImage = true;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 关闭弹窗\n\t\t\t */\n\t\t\tcloseModal() {\n\t\t\t\tthis.$emit('close');\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t/* 弹窗容器 */\n\t.history-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tz-index: 100;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t/* 遮罩层 */\n\t.modal-overlay {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t}\n\n\t/* 弹窗内容 */\n\t.modal-content {\n\t\tposition: relative;\n\t\twidth: 90%;\n\t\tmax-width: 700rpx;\n\t\tmax-height: 80%;\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);\n\t}\n\n\t/* 标题栏 */\n\t.modal-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 30rpx;\n\t\tborder-bottom: 2rpx solid #f0f0f0;\n\t\tbackground: #fafafa;\n\t}\n\n\t.modal-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.close-btn {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: 50%;\n\t\tbackground: #f5f5f5;\n\t}\n\n\t.close-icon {\n\t\tfont-size: 40rpx;\n\t\tcolor: #666666;\n\t\tfont-weight: bold;\n\t}\n\n\t/* 日期信息 */\n\t.date-info {\n\t\tpadding: 20rpx 30rpx;\n\t\tbackground: #f8f9fa;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tborder-bottom: 2rpx solid #f0f0f0;\n\t}\n\n\t.date-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tfont-weight: 500;\n\t}\n\n\t.count-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t}\n\n\t/* 加载状态 */\n\t.loading-container {\n\t\tpadding: 80rpx 30rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.loading-spinner {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tborder: 6rpx solid #f3f3f3;\n\t\tborder-top: 6rpx solid #007aff;\n\t\tborder-radius: 50%;\n\t\tanimation: spin 1s linear infinite;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t@keyframes spin {\n\t\t0% { transform: rotate(0deg); }\n\t\t100% { transform: rotate(360deg); }\n\t}\n\n\t.loading-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t}\n\n\t/* 空状态 */\n\t.empty-state {\n\t\tpadding: 80rpx 30rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.empty-icon {\n\t\tfont-size: 120rpx;\n\t\tmargin-bottom: 20rpx;\n\t\topacity: 0.5;\n\t\tdisplay: block;\n\t}\n\n\t.empty-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999999;\n\t}\n\n\t/* 记录列表容器 */\n\t.records-wrapper {\n\t\tflex: 1;\n\t\tpadding: 0 20rpx;\n\t\tmax-height: 800rpx;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\n\t/* 横向滚动容器 */\n\t.records-horizontal-scroll {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\twhite-space: nowrap;\n\t}\n\n\t/* 竖向滚动容器 */\n\t.records-vertical-scroll {\n\t\twidth: 100%;\n\t\theight: 800rpx;\n\t\tmin-width: 500rpx; /* 调整最小宽度，适合移动设备 */\n\t}\n\n\t/* 记录内容容器 */\n\t.records-content {\n\t\twidth: 100%;\n\t\tmin-width: 500rpx; /* 调整最小宽度，适合移动设备 */\n\t\tpadding-bottom: 20rpx;\n\t}\n\n\t/* 记录项 */\n\t.record-item {\n\t\tdisplay: flex;\n\t\tbackground: #ffffff;\n\t\tborder-radius: 15rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tpadding: 20rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\t\tborder: 2rpx solid #f0f0f0;\n\t\tmin-width: 480rpx; /* 调整最小宽度，适合移动设备 */\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t\talign-items: flex-start; /* 确保内容顶部对齐 */\n\t}\n\n\t/* 记录信息 */\n\t.record-info {\n\t\tflex: 1;\n\t\tmargin-right: 20rpx;\n\t\tmax-width: 420rpx; /* 限制信息区域最大宽度，为图片预留空间 */\n\t\tmin-width: 300rpx; /* 设置最小宽度，保证内容可读性 */\n\t}\n\n\t.record-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 15rpx;\n\t}\n\n\t.record-title {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333333;\n\t\tflex: 1;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t\tmax-width: 350rpx; /* 限制最大宽度，确保右边图片能显示 */\n\t}\n\n\t.record-time {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t\tmargin-left: 10rpx;\n\t}\n\n\t/* 详情项 */\n\t.record-details {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 8rpx;\n\t}\n\n\t.detail-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.detail-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t\twidth: 120rpx;\n\t\tflex-shrink: 0;\n\t}\n\n\t.detail-value {\n\t\tfont-size: 24rpx;\n\t\tcolor: #333333;\n\t\tflex: 1;\n\t}\n\n\t/* 识别结果文本特殊样式 */\n\t.detail-value.result-text {\n\t\tword-break: break-all;\n\t\twhite-space: normal;\n\t\tline-height: 1.4;\n\t\tmax-width: 300rpx;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\n\t/* 分类标签样式 */\n\t.category {\n\t\tpadding: 4rpx 12rpx;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 20rpx;\n\t\tcolor: white;\n\t\tfont-weight: 500;\n\t}\n\n\t.category-general {\n\t\tbackground: #007aff;\n\t}\n\n\t.category-registration {\n\t\tbackground: #34c759;\n\t}\n\n\t.category-carton {\n\t\tbackground: #ff9500;\n\t}\n\n\t.category-unknown {\n\t\tbackground: #8e8e93;\n\t}\n\n\t/* 图片预览 */\n\t.record-image {\n\t\tposition: relative;\n\t\twidth: 120rpx;\n\t\theight: 120rpx;\n\t\tborder-radius: 10rpx;\n\t\toverflow: hidden;\n\t\tflex-shrink: 0;\n\t}\n\n\t.preview-img {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.preview-overlay {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: rgba(0, 0, 0, 0.3);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\topacity: 0;\n\t\ttransition: opacity 0.3s;\n\t}\n\n\t.record-image:active .preview-overlay {\n\t\topacity: 1;\n\t}\n\n\t.preview-text {\n\t\tfont-size: 20rpx;\n\t\tcolor: white;\n\t\ttext-align: center;\n\t}\n\n\t/* 无图片状态 */\n\t.no-image {\n\t\twidth: 120rpx;\n\t\theight: 120rpx;\n\t\tborder-radius: 10rpx;\n\t\tbackground: #f5f5f5;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tflex-shrink: 0;\n\t}\n\n\t.no-image-icon {\n\t\tfont-size: 40rpx;\n\t\tmargin-bottom: 5rpx;\n\t\topacity: 0.5;\n\t}\n\n\t.no-image-text {\n\t\tfont-size: 20rpx;\n\t\tcolor: #999999;\n\t\ttext-align: center;\n\t}\n\n\t/* 底部操作 */\n\t.modal-footer {\n\t\tdisplay: flex;\n\t\tpadding: 20rpx 30rpx;\n\t\tborder-top: 2rpx solid #f0f0f0;\n\t\tbackground: #fafafa;\n\t\tgap: 20rpx;\n\t}\n\n\t.refresh-btn,\n\t.close-btn-footer {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tborder-radius: 15rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 28rpx;\n\t\tborder: none;\n\t}\n\n\t.refresh-btn {\n\t\tbackground: #007aff;\n\t\tcolor: white;\n\t}\n\n\t.close-btn-footer {\n\t\tbackground: #f5f5f5;\n\t\tcolor: #333333;\n\t}\n\n\t.btn-icon {\n\t\tmargin-right: 8rpx;\n\t}\n\n\t.btn-text {\n\t\tfont-size: 28rpx;\n\t}\n\n\t/* 滚动条样式优化 */\n\t.records-horizontal-scroll::-webkit-scrollbar,\n\t.records-vertical-scroll::-webkit-scrollbar {\n\t\twidth: 8rpx;\n\t\theight: 8rpx;\n\t}\n\n\t.records-horizontal-scroll::-webkit-scrollbar-track,\n\t.records-vertical-scroll::-webkit-scrollbar-track {\n\t\tbackground: #f1f1f1;\n\t\tborder-radius: 4rpx;\n\t}\n\n\t.records-horizontal-scroll::-webkit-scrollbar-thumb,\n\t.records-vertical-scroll::-webkit-scrollbar-thumb {\n\t\tbackground: #c1c1c1;\n\t\tborder-radius: 4rpx;\n\t}\n\n\t.records-horizontal-scroll::-webkit-scrollbar-thumb:hover,\n\t.records-vertical-scroll::-webkit-scrollbar-thumb:hover {\n\t\tbackground: #a8a8a8;\n\t}\n\n\t/* 滚动条角落 */\n\t.records-horizontal-scroll::-webkit-scrollbar-corner,\n\t.records-vertical-scroll::-webkit-scrollbar-corner {\n\t\tbackground: #f1f1f1;\n\t}\n\n\t/* 移动端滚动条指示器 */\n\t.records-horizontal-scroll,\n\t.records-vertical-scroll {\n\t\tscrollbar-width: thin;\n\t\tscrollbar-color: #c1c1c1 #f1f1f1;\n\t}\n</style>\n", "import Component from 'D:/Desktop/Warehouse/front/components/HistoryModal.vue'\nwx.createComponent(Component)"], "names": ["uni", "warehouseUserManager", "showError", "apiService", "showSuccess", "urlConfig"], "mappings": ";;;;;;AAiHC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA,IACN,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,SAAS,CAAE;AAAA,MACX,aAAa;AAAA,IACd;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,QAAQ,QAAQ;AACf,UAAI,QAAQ;AACX,aAAK,iBAAgB;AACrB,aAAK,kBAAiB;AAAA,MACvB;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAIR,MAAM,mBAAmB;AACxB,WAAK,UAAU;AACf,UAAI;AACHA,sBAAAA,MAAA,MAAA,OAAA,sCAAY,kBAAkB;AAG9B,cAAM,cAAcC,0CAAqB;AACzC,YAAI,CAAC,aAAa;AACjBC,wBAAS,UAAC,MAAM;AAChB,eAAK,UAAU;AACf;AAAA,QACD;AAEA,cAAM,WAAW,YAAY;AAC7BF,+EAAY,WAAW,QAAQ,QAAQ;AAEvC,cAAM,SAAS,MAAMG,iBAAAA,WAAW,gBAAgB,QAAQ;AACxD,aAAK,UAAU,OAAO,QAAQ,CAAA;AAC9BH,sBAAAA,MAAY,MAAA,OAAA,sCAAA,YAAY,KAAK,QAAQ,MAAM,MAAM;AAEjD,YAAI,KAAK,QAAQ,SAAS,GAAG;AAC5BI,wBAAW,YAAC,OAAO,KAAK,QAAQ,MAAM,QAAQ;AAAA,eACxC;AACNA,wBAAW,YAAC,UAAU;AAAA,QACvB;AAAA,MACC,SAAO,OAAO;AACfJ,sBAAA,MAAA,MAAA,SAAA,sCAAc,eAAe,KAAK;AAClCE,sBAAAA,UAAU,eAAe,MAAM,OAAO;AACtC,aAAK,UAAU;MAChB,UAAU;AACT,aAAK,UAAU;AAAA,MAChB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,oBAAoB;AACnB,YAAM,MAAM,oBAAI;AAChB,YAAM,OAAO,IAAI;AACjB,YAAM,QAAQ,OAAO,IAAI,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACxD,YAAM,MAAM,OAAO,IAAI,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AACjD,YAAM,WAAW,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AACjE,YAAM,UAAU,SAAS,IAAI,OAAQ,CAAA;AAErC,WAAK,cAAc,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,IACtD;AAAA;AAAA;AAAA;AAAA,IAKD,iBAAiB,MAAM;AACtB,YAAM,WAAW;AAAA,QAChB,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,WAAW;AAAA;AAEZ,aAAO,SAAS,IAAI,KAAK;AAAA,IACzB;AAAA;AAAA;AAAA;AAAA,IAKD,eAAe,OAAO;AACrB,UAAI,CAAC,SAAS,UAAU;AAAG,eAAO;AAElC,YAAM,QAAQ,CAAC,KAAK,MAAM,MAAM,IAAI;AACpC,UAAI,OAAO;AACX,UAAI,YAAY;AAEhB,aAAO,QAAQ,QAAQ,YAAY,MAAM,SAAS,GAAG;AACpD,gBAAQ;AACR;AAAA,MACD;AAEA,aAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,IAAI,MAAM,SAAS,CAAC;AAAA,IAC7C;AAAA;AAAA;AAAA;AAAA,IAKD,YAAY,WAAW;AACtB,aAAOG,gBAAS,UAAC,mBAAmB,SAAS;AAAA,IAC7C;AAAA;AAAA;AAAA;AAAA,IAKD,aAAa,QAAQ;AACpBL,oBAAA,MAAA,MAAA,OAAA,sCAAY,aAAa,OAAO,SAAS;AAEzC,YAAM,WAAW,KAAK,YAAY,OAAO,SAAS;AAElDA,oBAAAA,MAAI,aAAa;AAAA,QAChB,MAAM,CAAC,QAAQ;AAAA,QACf,SAAS;AAAA,QACT,SAAS,MAAM;AACdA,wBAAAA,yDAAY,UAAU;AAAA,QACtB;AAAA,QACD,MAAM,CAAC,UAAU;AAChBA,mFAAc,aAAa,KAAK;AAChCE,wBAAS,UAAC,QAAQ;AAAA,QACnB;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,aAAa,QAAQ,OAAO;AAC3BF,oBAAA,MAAA,MAAA,SAAA,sCAAc,aAAa,OAAO,SAAS;AAE3C,WAAK,QAAQ,KAAK,EAAE,WAAW;AAAA,IAC/B;AAAA;AAAA;AAAA;AAAA,IAKD,YAAY,QAAQ,OAAO;AAC1BA,oBAAA,MAAA,MAAA,OAAA,sCAAY,aAAa,OAAO,SAAS;AAEzC,WAAK,QAAQ,KAAK,EAAE,WAAW;AAAA,IAC/B;AAAA;AAAA;AAAA;AAAA,IAKD,aAAa;AACZ,WAAK,MAAM,OAAO;AAAA,IACnB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/QD,GAAG,gBAAgB,SAAS;"}