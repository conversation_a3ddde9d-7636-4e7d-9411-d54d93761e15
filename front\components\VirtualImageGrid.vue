<template>
	<view class="virtual-image-grid-container" :style="{ height: containerHeight + 'rpx' }">
		<scroll-view
			class="virtual-scroll"
			scroll-y="true"
			:scroll-top="scrollTop"
			@scroll="handleScroll"
			:refresher-enabled="refresherEnabled"
			:refresher-triggered="refresherTriggered"
			@refresherrefresh="handleRefresh"
			:style="{ height: containerHeight + 'rpx' }"
		>
			<!-- 上方占位区域 -->
			<view 
				class="virtual-placeholder-top" 
				:style="{ height: topPlaceholderHeight + 'rpx' }"
			></view>
			
			<!-- 可视区域内容 -->
			<view class="virtual-content">
				<view 
					class="grid-row" 
					v-for="(row, rowIndex) in visibleRows" 
					:key="'row-' + (startRowIndex + rowIndex)"
				>
					<view 
						class="image-item" 
						v-for="(image, colIndex) in row" 
						:key="getImageKey(image)"
						@click="handleImageClick(image, getImageIndex(rowIndex, colIndex))"
						@longpress="handleImageLongPress(image)"
					>
						<!-- 选择状态覆盖层 -->
						<view class="select-overlay" v-if="isSelectMode">
							<view 
								class="select-checkbox" 
								:class="{ 'selected': isImageSelected(image.id) }"
							>
								<text class="checkbox-icon" v-if="isImageSelected(image.id)">✓</text>
							</view>
						</view>

						<!-- 懒加载图片组件 -->
						<image
							class="image"
							:src="getImageUrl(image.image_path, image.id)"
							mode="aspectFill"
							:lazy-load="true"
							@error="handleImageError"
						/>

						<!-- 图片信息 -->
						<view class="image-info">
							<text class="image-name">{{ image.image_name }}</text>
							<text class="upload-time">{{ formatTime(image.upload_date) }}</text>
						</view>
					</view>
					
					<!-- 填充空白项（如果行不满） -->
					<view 
						class="image-item placeholder" 
						v-for="n in getPlaceholderCount(row.length)" 
						:key="'placeholder-' + n"
					></view>
				</view>
			</view>
			
			<!-- 下方占位区域 -->
			<view 
				class="virtual-placeholder-bottom" 
				:style="{ height: bottomPlaceholderHeight + 'rpx' }"
			></view>
		</scroll-view>
	</view>
</template>

<script>
import urlConfig from '@/utils/urlConfig.js';

export default {
	name: 'VirtualImageGrid',
	props: {
		// 图片数据列表
		images: {
			type: Array,
			default: () => []
		},
		// 每行显示的列数
		columns: {
			type: Number,
			default: 2
		},
		// 每行的高度（rpx）
		rowHeight: {
			type: Number,
			default: 300
		},
		// 容器高度（rpx）
		containerHeight: {
			type: Number,
			default: 800
		},
		// 缓冲区大小（额外渲染的行数）
		bufferSize: {
			type: Number,
			default: 2
		},
		// 选择模式
		isSelectMode: {
			type: Boolean,
			default: false
		},
		// 已选择的图片ID列表
		selectedImages: {
			type: Array,
			default: () => []
		},
		// 下拉刷新相关
		refresherEnabled: {
			type: Boolean,
			default: false
		},
		refresherTriggered: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			scrollTop: 0,
			startRowIndex: 0,
			endRowIndex: 0,
			visibleRowCount: 0
		}
	},
	computed: {
		// 将图片数据按行分组
		imageRows() {
			const rows = [];
			for (let i = 0; i < this.images.length; i += this.columns) {
				rows.push(this.images.slice(i, i + this.columns));
			}
			return rows;
		},
		
		// 可视区域内的行
		visibleRows() {
			const start = Math.max(0, this.startRowIndex - this.bufferSize);
			const end = Math.min(this.imageRows.length, this.endRowIndex + this.bufferSize);
			return this.imageRows.slice(start, end);
		},
		
		// 上方占位高度
		topPlaceholderHeight() {
			const start = Math.max(0, this.startRowIndex - this.bufferSize);
			return start * this.rowHeight;
		},
		
		// 下方占位高度
		bottomPlaceholderHeight() {
			const end = Math.min(this.imageRows.length, this.endRowIndex + this.bufferSize);
			return (this.imageRows.length - end) * this.rowHeight;
		}
	},
	watch: {
		images: {
			handler() {
				this.calculateVisibleRange();
			},
			immediate: true
		},
		containerHeight() {
			this.calculateVisibleRange();
		},
		rowHeight() {
			this.calculateVisibleRange();
		}
	},
	methods: {
		// 处理滚动事件
		handleScroll(e) {
			const scrollTop = e.detail.scrollTop;
			this.scrollTop = scrollTop;
			this.calculateVisibleRange(scrollTop);
			this.$emit('scroll', e);
		},
		
		// 计算可视区域范围
		calculateVisibleRange(scrollTop = this.scrollTop) {
			// 转换为实际像素值进行计算
			const scrollTopPx = scrollTop;
			const containerHeightPx = this.containerHeight / 2; // rpx转px的近似比例
			const rowHeightPx = this.rowHeight / 2;
			
			// 计算可视区域内的行数
			this.visibleRowCount = Math.ceil(containerHeightPx / rowHeightPx) + 1;
			
			// 计算开始和结束行索引
			this.startRowIndex = Math.floor(scrollTopPx / rowHeightPx);
			this.endRowIndex = this.startRowIndex + this.visibleRowCount;
			
			// 确保索引在有效范围内
			this.startRowIndex = Math.max(0, this.startRowIndex);
			this.endRowIndex = Math.min(this.imageRows.length, this.endRowIndex);
		},
		
		// 获取图片的唯一标识
		getImageKey(image) {
			return image.id || image.image_name;
		},
		
		// 获取图片在原始数组中的索引
		getImageIndex(rowIndex, colIndex) {
			const actualRowIndex = this.startRowIndex - this.bufferSize + rowIndex;
			return actualRowIndex * this.columns + colIndex;
		},
		
		// 获取占位符数量
		getPlaceholderCount(rowLength) {
			return Math.max(0, this.columns - rowLength);
		},
		
		// 检查图片是否被选中
		isImageSelected(imageId) {
			return this.selectedImages.includes(imageId);
		},
		
		// 处理图片点击
		handleImageClick(image, index) {
			this.$emit('image-click', { image, index });
		},
		
		// 处理图片长按
		handleImageLongPress(image) {
			this.$emit('image-longpress', image);
		},
		
		// 获取图片URL
		getImageUrl(imagePath, imageId) {
			// 触发事件让父组件处理URL生成
			this.$emit('get-image-url', { imagePath, imageId });

			// 使用统一的URL配置工具
			return urlConfig.getImageUrl(imagePath, imageId);
		},
		
		// 处理图片加载错误
		handleImageError(e) {
			this.$emit('image-error', e);
		},
		
		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return '未知';
			try {
				const date = new Date(timeStr);
				return date.toLocaleString();
			} catch (error) {
				return '未知';
			}
		},
		
		// 处理下拉刷新
		handleRefresh() {
			this.$emit('refresh');
		},
		
		// 滚动到指定位置
		scrollToRow(rowIndex) {
			const scrollTop = rowIndex * this.rowHeight / 2; // rpx转px
			this.scrollTop = scrollTop;
		},
		
		// 滚动到顶部
		scrollToTop() {
			this.scrollToRow(0);
		}
	},
	mounted() {
		this.calculateVisibleRange();
	}
}
</script>

<style scoped>
.virtual-image-grid-container {
	position: relative;
	overflow: hidden;
}

.virtual-scroll {
	width: 100%;
	height: 100%;
}

.virtual-placeholder-top,
.virtual-placeholder-bottom {
	width: 100%;
}

.virtual-content {
	width: 100%;
	padding: 0 20rpx;
}

.grid-row {
	display: flex;
	gap: 20rpx;
	margin-bottom: 20rpx;
}

.image-item {
	position: relative;
	flex: 1;
	background: #f8f9fa;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.image-item.placeholder {
	visibility: hidden;
}

.select-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.3);
	z-index: 2;
	display: flex;
	align-items: flex-start;
	justify-content: flex-end;
	padding: 15rpx;
}

.select-checkbox {
	width: 50rpx;
	height: 50rpx;
	border: 3rpx solid #ffffff;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.select-checkbox.selected {
	background: #4CAF50;
	border-color: #4CAF50;
}

.checkbox-icon {
	font-size: 28rpx;
	color: #ffffff;
	font-weight: bold;
}

.image {
	width: 100%;
	height: 200rpx;
}

.image-info {
	padding: 20rpx 15rpx;
}

.image-name {
	font-size: 26rpx;
	color: #333333;
	margin-bottom: 8rpx;
	display: block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.upload-time {
	font-size: 22rpx;
	color: #999999;
	display: block;
}
</style>
