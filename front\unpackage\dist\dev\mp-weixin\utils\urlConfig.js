"use strict";
function getApiBaseUrl() {
  return "https://www.mls2005.top";
}
function getApiUrl(path) {
  const baseUrl = getApiBaseUrl();
  const normalizedPath = path.startsWith("/") ? path : `/${path}`;
  return `${baseUrl}${normalizedPath}`;
}
function getImageUrl(imagePath, imageId = null) {
  if (imagePath && imagePath.startsWith("http")) {
    return imagePath;
  }
  const baseUrl = getApiBaseUrl();
  if (imageId) {
    return `${baseUrl}/api/images/file/${imageId}`;
  }
  return imagePath || "";
}
function getUploadUrl() {
  return getApiUrl("/api/images/upload");
}
function getHistoryImageUrl(imagePath) {
  if (!imagePath)
    return "";
  let processedPath = imagePath;
  if (imagePath.startsWith("C:\\MLS\\Warehouse_Img\\")) {
    processedPath = imagePath.replace("C:\\MLS\\Warehouse_Img\\", "");
  } else if (imagePath.startsWith("C:/MLS/Warehouse_Img/")) {
    processedPath = imagePath.replace("C:/MLS/Warehouse_Img/", "");
  } else if (imagePath.startsWith("C:\\Warehouse_Img\\")) {
    processedPath = imagePath.replace("C:\\Warehouse_Img\\", "");
  } else if (imagePath.startsWith("C:/Warehouse_Img/")) {
    processedPath = imagePath.replace("C:/Warehouse_Img/", "");
  }
  const urlPath = processedPath.replace(/\\/g, "/");
  const encodedPath = urlPath.split("/").map((segment) => encodeURIComponent(segment)).join("/");
  return getApiUrl(`/api/history/image/${encodedPath}`);
}
const ENV = {
  // 是否为微信小程序环境
  isWeixin: () => {
    return true;
  },
  // 是否为H5环境
  isH5: () => {
    return false;
  },
  // 是否为HTTPS环境
  isHttps: () => {
    return true;
  },
  // 是否为生产环境
  isProduction: () => {
    return ENV.isWeixin() || ENV.isHttps();
  }
};
const urlConfig = {
  getApiBaseUrl,
  getApiUrl,
  getImageUrl,
  getUploadUrl,
  getHistoryImageUrl,
  ENV
};
exports.urlConfig = urlConfig;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/urlConfig.js.map
