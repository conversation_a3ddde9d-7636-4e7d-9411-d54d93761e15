# 工厂订单图片管理系统 - 技术文档

## 项目概述

### 项目信息
- **项目名称**: 工厂订单图片管理系统
- **技术栈**: uni-app + Express.js + MySQL
- **部署方式**: Nginx反向代理 + HTTPS
- **目标平台**: 微信小程序、H5
- **服务器**: ************ (HTTPS: www.mls2005.top)

### 业务场景
本系统为工厂订单图片上传与管理系统，支持工厂用户登录后上传订单相关图片，实现图片的分类存储、查询和管理功能。

## 系统架构

### 技术架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端(uni-app)  │    │   后端(Express)  │    │   数据库(MySQL)  │
│                 │    │                 │    │                 │
│ • 微信小程序     │◄──►│ • RESTful API   │◄──►│ • Factory_Login │
│ • H5页面        │    │ • 文件上传      │    │ • Img_Info      │
│ • 移动端适配     │    │ • 用户认证      │    │ • 连接池管理     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └─────────────►│   文件存储系统   │◄─────────────┘
                        │                 │
                        │ C:\MLS\Order_Img│
                        │ └── [工厂名]/   │
                        │     └── [年-月]/│
                        │         └── [日]/│
                        │           └── [订单号]/│
                        └─────────────────┘
```

### 项目目录结构

#### 前端目录 (front/)
```
front/
├── pages/                      # 页面文件
│   ├── home/                   # 首页
│   ├── login/                  # 工厂登录页
│   ├── warehouseLogin/         # 仓库登录页
│   ├── order/                  # 订单管理页
│   ├── imageManage/            # 图片管理页
│   ├── camera/                 # 拍照上传页
│   └── index/                  # 仓库管理页
├── components/                 # 公共组件
│   ├── HistoryModal.vue        # 历史记录弹窗
│   ├── ImagePicker.vue         # 图片选择器
│   ├── VirtualImageGrid.vue    # 虚拟图片网格
│   ├── LazyImage.vue          # 懒加载图片组件
│   └── ...
├── utils/                      # 工具类
│   ├── apiService.js          # API服务封装
│   ├── userManager.js         # 用户管理
│   ├── helpers.js             # 工具函数
│   ├── imageCache.js          # 图片缓存
│   └── ...
├── static/                     # 静态资源
├── uni.scss                   # 全局样式
├── manifest.json              # 项目配置
├── pages.json                 # 页面配置
└── App.vue                    # 应用入口
```

#### 后端目录 (server/)
```
server/
├── config/                     # 配置文件
│   └── database.js            # 数据库配置与连接池
├── routes/                     # 路由模块
│   ├── auth.js                # 用户认证
│   ├── orders.js              # 订单管理
│   ├── images.js              # 图片管理
│   ├── upload.js              # 文件上传
│   └── index.js               # 路由汇总
├── middleware/                 # 中间件
│   ├── logger.js              # 日志中间件
│   ├── errorHandler.js        # 错误处理
│   └── dbMonitor.js           # 数据库监控
├── utils/                      # 工具类
│   └── asyncFileUtils.js      # 异步文件操作
├── logs/                       # 日志文件
├── ssl/                        # SSL证书
└── app.js                     # 应用入口
```

## 功能模块设计

### 1. 用户认证模块

#### 功能特性
- 工厂用户登录/登出
- 仓库用户登录（独立系统）
- Token-based认证
- 登录状态持久化

#### 实现方式
- 前端使用 `userManager.js` 管理用户状态
- 后端简化Token实现（Base64编码，2小时有效期）
- 登录信息存储在 `uni.storage` 中

### 2. 订单管理模块

#### 功能特性
- 订单号输入与验证
- 新增订单功能
- 订单模糊查询
- 历史订单列表
- 订单统计信息

#### 关键接口
```javascript
// 获取订单历史
GET /api/orders/history

// 模糊查询订单
GET /api/orders/query/:orderNumber

// 获取订单图片列表
GET /api/orders/:orderNumber/images

// 获取订单统计
GET /api/orders/stats
```

### 3. 图片管理模块

#### 功能特性
- 图片分页加载
- 图片懒加载
- 图片预览放大
- 批量删除功能
- 虚拟滚动优化

#### 存储结构
```
C:\MLS\Order_Img\
└── [工厂名]/
    └── [年-月]/
        └── [日]/
            └── [订单号]/
                ├── 图片1_时间戳_随机数.jpg
                ├── 图片2_时间戳_随机数.jpg
                └── ...
```

### 4. 图片上传模块

#### 功能特性
- 拍照上传
- 相册选择
- 多图片上传（最多9张）
- 上传进度显示
- 文件大小限制

#### 文件命名规则
```
格式: 时间戳_随机数.扩展名
示例: 1752593301178_2207b657c57949b99f1e7d3b6b9e0a71.png
```

## 数据库设计

### 数据库连接配置
```javascript
const dbConfig = {
    host: '************',
    port: 3306,
    user: 'mls01',
    password: '12345@Mls',
    database: 'identify',
    charset: 'utf8mb4',
    timezone: '+08:00'
};
```

### 连接池配置
```javascript
const poolConfig = {
    connectionLimit: 100,           // 最大连接数
    acquireTimeout: 60000,          // 获取连接超时
    queueLimit: 200,                // 等待队列长度
    waitForConnections: true,       // 等待可用连接
    idleTimeout: 300000,            // 空闲连接超时
    maxIdle: 20,                    // 最大空闲连接数
};
```

### 核心数据表

#### Factory_Login 表（工厂登录）
```sql
CREATE TABLE Factory_Login (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    factory_name VARCHAR(100) NOT NULL,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Img_Info 表（图片信息）
```sql
CREATE TABLE Img_Info (
    id INT PRIMARY KEY AUTO_INCREMENT,
    factory_name VARCHAR(100) NOT NULL,
    order_number VARCHAR(100) NOT NULL,
    image_name VARCHAR(255) NOT NULL,
    image_path VARCHAR(500) NOT NULL,
    file_size BIGINT DEFAULT 0,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_factory_order (factory_name, order_number),
    INDEX idx_upload_date (upload_date)
);
```

## API接口文档

### 认证接口

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
    "username": "工厂用户名",
    "password": "密码"
}

Response:
{
    "success": true,
    "message": "登录成功",
    "data": {
        "username": "factory01",
        "factory_name": "测试工厂001",
        "token": "eyJ0eXAiOiJKV1...",
        "expires_in": 7200
    }
}
```

#### 获取用户信息
```http
GET /api/auth/profile
Authorization: Bearer <token>

Response:
{
    "success": true,
    "data": {
        "id": 1,
        "username": "factory01",
        "factory_name": "测试工厂001",
        "created_at": "2024-01-01 10:00:00"
    }
}
```

### 订单接口

#### 获取订单历史
```http
GET /api/orders/history
Authorization: Bearer <token>

Response:
{
    "success": true,
    "message": "获取订单历史成功",
    "data": [
        {
            "order_number": "ORD-2024-001",
            "created_date": "2024-01-01 10:00:00",
            "latest_upload": "2024-01-01 15:30:00",
            "image_count": 5
        }
    ]
}
```

#### 模糊查询订单
```http
GET /api/orders/query/:orderNumber
Authorization: Bearer <token>

Response:
{
    "success": true,
    "message": "找到 3 个匹配的订单",
    "data": [
        {
            "order_number": "ORD-2024-001",
            "created_date": "2024-01-01 10:00:00",
            "latest_upload": "2024-01-01 15:30:00",
            "image_count": 5
        }
    ],
    "count": 3,
    "searchTerm": "ORD-2024"
}
```

### 图片接口

#### 获取订单图片列表
```http
GET /api/orders/:orderNumber/images?page=1&limit=20
Authorization: Bearer <token>

Response:
{
    "success": true,
    "message": "获取图片列表成功",
    "data": [
        {
            "id": 1,
            "image_name": "1752593301178_2207b657c57949b99f1e7d3b6b9e0a71.png",
            "image_path": "/order-images/测试工厂003/2025-07/010/gonggao1_1752593301178.png",
            "file_size": 524288,
            "upload_date": "2024-01-01 10:00:00"
        }
    ],
    "pagination": {
        "page": 1,
        "limit": 20,
        "total": 45,
        "totalPages": 3,
        "hasNext": true,
        "hasPrev": false
    }
}
```

#### 上传图片
```http
POST /api/images/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

FormData:
- order_number: "ORD-2024-001"
- images: [File1, File2, ...]

Response:
{
    "success": true,
    "message": "上传成功",
    "data": {
        "uploaded_count": 2,
        "failed_count": 0,
        "results": [
            {
                "id": 1,
                "image_name": "image1.jpg",
                "image_path": "/order-images/...",
                "file_size": 524288
            }
        ]
    }
}
```

#### 删除图片
```http
DELETE /api/images/batch-delete
Authorization: Bearer <token>
Content-Type: application/json

{
    "image_ids": [1, 2, 3],
    "order_number": "ORD-2024-001"
}

Response:
{
    "success": true,
    "message": "删除成功",
    "data": {
        "deleted_count": 3,
        "failed_count": 0
    }
}
```

## 前端技术要点

### 页面配置 (pages.json)
```json
{
    "pages": [
        {
            "path": "pages/home/<USER>",
            "style": {
                "navigationBarTitleText": "智能管理系统",
                "navigationBarBackgroundColor": "#667eea",
                "navigationBarTextStyle": "white"
            }
        },
        {
            "path": "pages/login/login",
            "style": {
                "navigationBarTitleText": "工厂用户登录"
            }
        },
        {
            "path": "pages/order/order",
            "style": {
                "navigationBarTitleText": "订单管理"
            }
        },
        {
            "path": "pages/imageManage/imageManage",
            "style": {
                "navigationBarTitleText": "图片管理"
            }
        },
        {
            "path": "pages/camera/camera",
            "style": {
                "navigationBarTitleText": "拍照上传"
            }
        }
    ],
    "globalStyle": {
        "navigationBarTextStyle": "white",
        "navigationBarTitleText": "订单图片管理系统",
        "navigationBarBackgroundColor": "#667eea",
        "backgroundColor": "#f5f5f5"
    }
}
```

### 用户管理工具类
```javascript
// userManager.js 核心功能
class UserManager {
    // 登录
    login(userData) {
        try {
            uni.setStorageSync('user_token', userData.token);
            uni.setStorageSync('user_info', userData);
            this.userInfo = userData;
            return true;
        } catch (error) {
            return false;
        }
    }

    // 检查登录状态
    checkLoginStatus() {
        try {
            const token = uni.getStorageSync('user_token');
            const userInfo = uni.getStorageSync('user_info');
            return !!(token && userInfo);
        } catch (error) {
            return false;
        }
    }

    // 创建认证请求
    createAuthRequest(config) {
        const token = uni.getStorageSync('user_token');
        if (token) {
            config.header = config.header || {};
            config.header['Authorization'] = `Bearer ${token}`;
        }
        return config;
    }
}
```

### 图片组件优化

#### 懒加载图片组件
```vue
<!-- LazyImage.vue -->
<template>
    <view class="lazy-image-container" :style="containerStyle">
        <image
            v-if="shouldLoad"
            :src="imageSrc"
            :mode="mode"
            :lazy-load="true"
            @load="onImageLoad"
            @error="onImageError"
            :class="imageClass"
        />
        <view v-else class="image-placeholder">
            <text class="placeholder-text">📷</text>
        </view>
    </view>
</template>
```

#### 虚拟滚动网格
```vue
<!-- VirtualImageGrid.vue -->
<template>
    <scroll-view
        class="virtual-grid"
        :scroll-y="true"
        :scroll-top="scrollTop"
        @scroll="onScroll"
    >
        <view
            v-for="item in visibleItems"
            :key="item.id"
            class="grid-item"
            :style="item.style"
        >
            <LazyImage :src="item.src" @click="onImageClick(item)" />
        </view>
    </scroll-view>
</template>
```

## 后端技术要点

### 数据库连接池监控
```javascript
// 连接池统计
let poolStats = {
    totalConnections: 0,
    activeConnections: 0,
    queuedRequests: 0,
    totalQueries: 0,
    failedQueries: 0,
    avgQueryTime: 0
};

// 性能监控查询
async function query(sql, params = []) {
    const startTime = Date.now();
    let connection = null;

    try {
        connection = await pool.getConnection();
        const [rows] = await connection.execute(sql, params);
        
        const queryTime = Date.now() - startTime;
        poolStats.totalQueries++;
        poolStats.avgQueryTime = Math.round(
            (poolStats.avgQueryTime * (poolStats.totalQueries - 1) + queryTime) / poolStats.totalQueries
        );

        return rows;
    } catch (error) {
        poolStats.failedQueries++;
        throw error;
    } finally {
        if (connection) connection.release();
    }
}
```

### 文件上传处理
```javascript
// multer 配置
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        
        const orderNumber = req.body.order_number || 'unknown';
        const uploadPath = path.join('C:', 'MLS', 'Order_Img', 'factory_temp', 
                                    `${year}-${month}`, day, orderNumber);
        
        if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
        }
        
        cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
        const timestamp = Date.now();
        const randomNum = Math.random().toString(36).substr(2, 9);
        const ext = path.extname(file.originalname);
        const filename = `${timestamp}_${randomNum}${ext}`;
        cb(null, filename);
    }
});
```

### 错误处理中间件
```javascript
// 全局错误处理
const globalErrorHandler = (err, req, res, next) => {
    console.error('❌ 服务器错误:', err);
    
    // 数据库错误
    if (err.code === 'ER_DUP_ENTRY') {
        return res.status(400).json({
            success: false,
            message: '数据重复，请检查输入'
        });
    }
    
    // 文件上传错误
    if (err instanceof multer.MulterError) {
        return res.status(400).json({
            success: false,
            message: '文件上传失败: ' + err.message
        });
    }
    
    // 默认错误响应
    res.status(500).json({
        success: false,
        message: '服务器内部错误'
    });
};
```

## 部署配置

### Nginx 配置
```nginx
server {
    listen 443 ssl http2;
    server_name www.mls2005.top;
    
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    # 代理API请求
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 静态文件服务
    location /order-images/ {
        alias C:/MLS/Order_Img/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}
```

### 环境变量配置
```bash
# .env 文件
NODE_ENV=production
PORT=3000
DB_HOST=************
DB_PORT=3306
DB_USER=mls01
DB_PASSWORD=12345@Mls
DB_NAME=identify
```

## 性能优化

### 前端优化
1. **图片懒加载**: 减少初始加载时间
2. **虚拟滚动**: 处理大量图片列表
3. **图片缓存**: 本地缓存常用图片
4. **分页加载**: 避免一次性加载过多数据
5. **请求防抖**: 防止重复请求

### 后端优化
1. **连接池管理**: 100个并发连接
2. **查询优化**: 合理使用索引
3. **文件流处理**: 大文件分片上传
4. **错误监控**: 详细的日志记录
5. **健康检查**: 定期检查系统状态

## 安全考虑

### 认证安全
- Token有效期控制（2小时）
- 请求头Token验证
- 用户权限隔离

### 文件安全
- 文件类型验证
- 文件大小限制
- 路径遍历防护
- 病毒扫描集成点

### 数据安全
- SQL注入防护
- XSS攻击防护
- HTTPS数据传输
- 敏感信息加密

## 监控与维护

### 系统监控
- 数据库连接池状态
- API响应时间监控
- 文件存储空间监控
- 错误日志收集

### 定期维护
- 日志文件清理
- 临时文件清理
- 数据库性能调优
- SSL证书更新

## 扩展计划

### 功能扩展
- 图片识别OCR集成
- 批量导出功能
- 订单状态管理
- 多工厂权限管理

### 技术升级
- JWT Token实现
- Redis缓存集成
- 微服务架构拆分
- 容器化部署

---

**文档版本**: v1.0  
**最后更新**: 2024年1月
**维护人员**: 系统开发团队 