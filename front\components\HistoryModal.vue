<template>
	<view class="history-modal" v-if="visible">
		<!-- 遮罩层 -->
		<view class="modal-overlay" @click="closeModal"></view>
		
		<!-- 弹窗内容 -->
		<view class="modal-content">
			<!-- 标题栏 -->
			<view class="modal-header">
				<text class="modal-title">📋 今日历史记录</text>
				<view class="close-btn" @click="closeModal">
					<text class="close-icon">×</text>
				</view>
			</view>
			
			<!-- 日期信息 -->
			<view class="date-info">
				<text class="date-text">{{currentDate}}</text>
				<text class="count-text">共 {{records.length}} 条记录</text>
			</view>
			
			<!-- 加载状态 -->
			<view class="loading-container" v-if="loading">
				<view class="loading-spinner"></view>
				<text class="loading-text">正在加载历史记录...</text>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-else-if="records.length === 0">
				<text class="empty-icon">📂</text>
				<text class="empty-text">今日暂无上传记录</text>
			</view>
			
			<!-- 记录列表 -->
			<view class="records-wrapper" v-else>
				<!-- 横向滚动容器 -->
				<scroll-view class="records-horizontal-scroll" scroll-x="true" scroll-y="false">
					<!-- 竖向滚动容器 -->
					<scroll-view class="records-vertical-scroll" scroll-y="true" scroll-x="false">
						<view class="records-content">
							<view class="record-item" v-for="(record, index) in records" :key="index">
								<!-- 记录信息 -->
								<view class="record-info">
									<view class="record-header">
										<text class="record-title">{{record.ImageName}}</text>
										<text class="record-time">{{record.UploadTime}}</text>
									</view>

									<view class="record-details">
										<view class="detail-item">
											<text class="detail-label">类型:</text>
											<text class="detail-value category" :class="getCategoryClass(record.recognitionType)">
												{{record.category}}
											</text>
										</view>

										<view class="detail-item" v-if="record.CartonNumber">
											<text class="detail-label">识别结果:</text>
											<text class="detail-value result-text">{{record.CartonNumber}}</text>
										</view>

										<view class="detail-item">
											<text class="detail-label">文件大小:</text>
											<text class="detail-value">{{formatFileSize(record.fileSize)}}</text>
										</view>
									</view>
								</view>

								<!-- 图片预览 -->
								<view class="record-image" v-if="record.hasImage !== false" @click="previewImage(record)">
									<image
										:src="getImageUrl(record.ImagePath)"
										mode="aspectFill"
										class="preview-img"
										@error="onImageError(record, index)"
										@load="onImageLoad(record, index)"
									></image>
									<view class="preview-overlay">
										<text class="preview-text">点击预览</text>
									</view>
								</view>

								<!-- 无图片状态 -->
								<view class="no-image" v-else>
									<text class="no-image-icon">🖼️</text>
									<text class="no-image-text">图片不可用</text>
								</view>
							</view>
						</view>
					</scroll-view>
				</scroll-view>
			</view>
			
			<!-- 底部操作 -->
			<view class="modal-footer">
				<button class="refresh-btn" @click="loadTodayHistory">
					<text class="btn-icon">🔄</text>
					<text class="btn-text">刷新</text>
				</button>
				<button class="close-btn-footer" @click="closeModal">
					<text class="btn-text">关闭</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import apiService from '@/utils/apiService.js';
	import { showError, showSuccess } from '@/utils/helpers.js';
	import warehouseUserManager from '@/utils/warehouseUsers.js';
	import urlConfig from '@/utils/urlConfig.js';

	export default {
		name: 'HistoryModal',
		props: {
			visible: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				loading: false,
				records: [],
				currentDate: ''
			}
		},
		watch: {
			visible(newVal) {
				if (newVal) {
					this.loadTodayHistory();
					this.updateCurrentDate();
				}
			}
		},
		methods: {
			/**
			 * 加载今日历史记录
			 */
			async loadTodayHistory() {
				this.loading = true;
				try {
					console.log('📋 开始加载今日历史记录...');

					// 获取当前登录用户的用户名
					const currentUser = warehouseUserManager.getCurrentUser();
					if (!currentUser) {
						showError('请先登录');
						this.records = [];
						return;
					}

					const username = currentUser.username;
					console.log(`📋 查询用户 ${username} 的历史记录`);

					const result = await apiService.getTodayHistory(username);
					this.records = result.data || [];
					console.log(`✅ 加载完成，共 ${this.records.length} 条记录`);

					if (this.records.length > 0) {
						showSuccess(`加载了 ${this.records.length} 条今日记录`);
					} else {
						showSuccess('今日暂无上传记录');
					}
				} catch (error) {
					console.error('❌ 加载历史记录失败:', error);
					showError('加载历史记录失败: ' + error.message);
					this.records = [];
				} finally {
					this.loading = false;
				}
			},

			/**
			 * 更新当前日期显示
			 */
			updateCurrentDate() {
				const now = new Date();
				const year = now.getFullYear();
				const month = String(now.getMonth() + 1).padStart(2, '0');
				const day = String(now.getDate()).padStart(2, '0');
				const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
				const weekday = weekdays[now.getDay()];
				
				this.currentDate = `${year}年${month}月${day}日 ${weekday}`;
			},

			/**
			 * 获取分类样式类名
			 */
			getCategoryClass(type) {
				const classMap = {
					'general': 'category-general',
					'registration': 'category-registration', 
					'carton_info': 'category-carton',
					'unknown': 'category-unknown'
				};
				return classMap[type] || 'category-general';
			},

			/**
			 * 格式化文件大小
			 */
			formatFileSize(bytes) {
				if (!bytes || bytes === 0) return '未知';
				
				const units = ['B', 'KB', 'MB', 'GB'];
				let size = bytes;
				let unitIndex = 0;
				
				while (size >= 1024 && unitIndex < units.length - 1) {
					size /= 1024;
					unitIndex++;
				}
				
				return `${size.toFixed(1)} ${units[unitIndex]}`;
			},

			/**
			 * 获取图片URL
			 */
			getImageUrl(imagePath) {
				return urlConfig.getHistoryImageUrl(imagePath);
			},

			/**
			 * 预览图片
			 */
			previewImage(record) {
				console.log('🖼️ 预览图片:', record.ImageName);
				
				const imageUrl = this.getImageUrl(record.ImagePath);
				
				uni.previewImage({
					urls: [imageUrl],
					current: imageUrl,
					success: () => {
						console.log('✅ 图片预览成功');
					},
					fail: (error) => {
						console.error('❌ 图片预览失败:', error);
						showError('图片预览失败');
					}
				});
			},

			/**
			 * 图片加载错误处理
			 */
			onImageError(record, index) {
				console.error('❌ 图片加载失败:', record.ImagePath);
				// 标记图片不可用
				this.records[index].hasImage = false;
			},

			/**
			 * 图片加载成功处理
			 */
			onImageLoad(record, index) {
				console.log('✅ 图片加载成功:', record.ImagePath);
				// 确保图片标记为可用
				this.records[index].hasImage = true;
			},

			/**
			 * 关闭弹窗
			 */
			closeModal() {
				this.$emit('close');
			}
		}
	}
</script>

<style scoped>
	/* 弹窗容器 */
	.history-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 100;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 遮罩层 */
	.modal-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
	}

	/* 弹窗内容 */
	.modal-content {
		position: relative;
		width: 90%;
		max-width: 700rpx;
		max-height: 80%;
		background: #ffffff;
		border-radius: 20rpx;
		display: flex;
		flex-direction: column;
		overflow: hidden;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
	}

	/* 标题栏 */
	.modal-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 2rpx solid #f0f0f0;
		background: #fafafa;
	}

	.modal-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.close-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background: #f5f5f5;
	}

	.close-icon {
		font-size: 40rpx;
		color: #666666;
		font-weight: bold;
	}

	/* 日期信息 */
	.date-info {
		padding: 20rpx 30rpx;
		background: #f8f9fa;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 2rpx solid #f0f0f0;
	}

	.date-text {
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
	}

	.count-text {
		font-size: 24rpx;
		color: #666666;
	}

	/* 加载状态 */
	.loading-container {
		padding: 80rpx 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 6rpx solid #f3f3f3;
		border-top: 6rpx solid #007aff;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.loading-text {
		font-size: 28rpx;
		color: #666666;
	}

	/* 空状态 */
	.empty-state {
		padding: 80rpx 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.empty-icon {
		font-size: 120rpx;
		margin-bottom: 20rpx;
		opacity: 0.5;
		display: block;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}

	/* 记录列表容器 */
	.records-wrapper {
		flex: 1;
		padding: 0 20rpx;
		max-height: 800rpx;
		position: relative;
		overflow: hidden;
	}

	/* 横向滚动容器 */
	.records-horizontal-scroll {
		width: 100%;
		height: 100%;
		white-space: nowrap;
	}

	/* 竖向滚动容器 */
	.records-vertical-scroll {
		width: 100%;
		height: 800rpx;
		min-width: 500rpx; /* 调整最小宽度，适合移动设备 */
	}

	/* 记录内容容器 */
	.records-content {
		width: 100%;
		min-width: 500rpx; /* 调整最小宽度，适合移动设备 */
		padding-bottom: 20rpx;
	}

	/* 记录项 */
	.record-item {
		display: flex;
		background: #ffffff;
		border-radius: 15rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		border: 2rpx solid #f0f0f0;
		min-width: 480rpx; /* 调整最小宽度，适合移动设备 */
		width: 100%;
		box-sizing: border-box;
		align-items: flex-start; /* 确保内容顶部对齐 */
	}

	/* 记录信息 */
	.record-info {
		flex: 1;
		margin-right: 20rpx;
		max-width: 420rpx; /* 限制信息区域最大宽度，为图片预留空间 */
		min-width: 300rpx; /* 设置最小宽度，保证内容可读性 */
	}

	.record-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
	}

	.record-title {
		font-size: 28rpx;
		font-weight: 500;
		color: #333333;
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		max-width: 350rpx; /* 限制最大宽度，确保右边图片能显示 */
	}

	.record-time {
		font-size: 24rpx;
		color: #666666;
		margin-left: 10rpx;
	}

	/* 详情项 */
	.record-details {
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}

	.detail-item {
		display: flex;
		align-items: center;
	}

	.detail-label {
		font-size: 24rpx;
		color: #666666;
		width: 120rpx;
		flex-shrink: 0;
	}

	.detail-value {
		font-size: 24rpx;
		color: #333333;
		flex: 1;
	}

	/* 识别结果文本特殊样式 */
	.detail-value.result-text {
		word-break: break-all;
		white-space: normal;
		line-height: 1.4;
		max-width: 300rpx;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	/* 分类标签样式 */
	.category {
		padding: 4rpx 12rpx;
		border-radius: 8rpx;
		font-size: 20rpx;
		color: white;
		font-weight: 500;
	}

	.category-general {
		background: #007aff;
	}

	.category-registration {
		background: #34c759;
	}

	.category-carton {
		background: #ff9500;
	}

	.category-unknown {
		background: #8e8e93;
	}

	/* 图片预览 */
	.record-image {
		position: relative;
		width: 120rpx;
		height: 120rpx;
		border-radius: 10rpx;
		overflow: hidden;
		flex-shrink: 0;
	}

	.preview-img {
		width: 100%;
		height: 100%;
	}

	.preview-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.3);
		display: flex;
		align-items: center;
		justify-content: center;
		opacity: 0;
		transition: opacity 0.3s;
	}

	.record-image:active .preview-overlay {
		opacity: 1;
	}

	.preview-text {
		font-size: 20rpx;
		color: white;
		text-align: center;
	}

	/* 无图片状态 */
	.no-image {
		width: 120rpx;
		height: 120rpx;
		border-radius: 10rpx;
		background: #f5f5f5;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
	}

	.no-image-icon {
		font-size: 40rpx;
		margin-bottom: 5rpx;
		opacity: 0.5;
	}

	.no-image-text {
		font-size: 20rpx;
		color: #999999;
		text-align: center;
	}

	/* 底部操作 */
	.modal-footer {
		display: flex;
		padding: 20rpx 30rpx;
		border-top: 2rpx solid #f0f0f0;
		background: #fafafa;
		gap: 20rpx;
	}

	.refresh-btn,
	.close-btn-footer {
		flex: 1;
		height: 80rpx;
		border-radius: 15rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		border: none;
	}

	.refresh-btn {
		background: #007aff;
		color: white;
	}

	.close-btn-footer {
		background: #f5f5f5;
		color: #333333;
	}

	.btn-icon {
		margin-right: 8rpx;
	}

	.btn-text {
		font-size: 28rpx;
	}

	/* 滚动条样式优化 */
	.records-horizontal-scroll::-webkit-scrollbar,
	.records-vertical-scroll::-webkit-scrollbar {
		width: 8rpx;
		height: 8rpx;
	}

	.records-horizontal-scroll::-webkit-scrollbar-track,
	.records-vertical-scroll::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 4rpx;
	}

	.records-horizontal-scroll::-webkit-scrollbar-thumb,
	.records-vertical-scroll::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 4rpx;
	}

	.records-horizontal-scroll::-webkit-scrollbar-thumb:hover,
	.records-vertical-scroll::-webkit-scrollbar-thumb:hover {
		background: #a8a8a8;
	}

	/* 滚动条角落 */
	.records-horizontal-scroll::-webkit-scrollbar-corner,
	.records-vertical-scroll::-webkit-scrollbar-corner {
		background: #f1f1f1;
	}

	/* 移动端滚动条指示器 */
	.records-horizontal-scroll,
	.records-vertical-scroll {
		scrollbar-width: thin;
		scrollbar-color: #c1c1c1 #f1f1f1;
	}
</style>
