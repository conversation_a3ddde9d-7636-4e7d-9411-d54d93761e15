{"version": 3, "file": "camera.js", "sources": ["pages/camera/camera.vue", "E:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2FtZXJhL2NhbWVyYS52dWU"], "sourcesContent": ["<template>\n\t<view class=\"camera-container\">\n\t\t<!-- 头部信息 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"order-info\">\n\t\t\t\t<text class=\"order-icon\">📦</text>\n\t\t\t\t<view class=\"order-details\">\n\t\t\t\t\t<text class=\"order-label\">订单号</text>\n\t\t\t\t\t<text class=\"order-number\">{{ orderNumber }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 拍照选择区域 -->\n\t\t<view class=\"camera-section\">\n\t\t\t<view class=\"section-title\">\n\t\t\t\t<text class=\"title-icon\">📷</text>\n\t\t\t\t<text class=\"title-text\">选择图片</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"camera-options\">\n\t\t\t\t<view class=\"option-card\" @click=\"takePhoto\">\n\t\t\t\t\t<view class=\"option-icon\">\n\t\t\t\t\t\t<text class=\"icon\">📸</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"option-text\">拍照</text>\n\t\t\t\t\t<text class=\"option-desc\">使用相机拍摄新照片</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"option-card\" @click=\"chooseFromAlbum\">\n\t\t\t\t\t<view class=\"option-icon\">\n\t\t\t\t\t\t<text class=\"icon\">🖼️</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"option-text\">相册</text>\n\t\t\t\t\t<text class=\"option-desc\">从相册选择图片</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 已选择图片预览 -->\n\t\t<view class=\"preview-section\" v-if=\"selectedImages.length > 0\">\n\t\t\t<view class=\"section-title\">\n\t\t\t\t<text class=\"title-icon\">🖼️</text>\n\t\t\t\t<text class=\"title-text\">已选择图片 ({{ selectedImages.length }})</text>\n\t\t\t</view>\n\n\t\t\t<scroll-view class=\"image-preview-list\" scroll-x=\"true\">\n\t\t\t\t<view \n\t\t\t\t\tclass=\"preview-item\" \n\t\t\t\t\tv-for=\"(image, index) in selectedImages\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t>\n\t\t\t\t\t<image \n\t\t\t\t\t\tclass=\"preview-image\" \n\t\t\t\t\t\t:src=\"image.path\" \n\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t/>\n\t\t\t\t\t<view class=\"remove-btn\" @click=\"removeImage(index)\">\n\t\t\t\t\t\t<text class=\"remove-icon\">✕</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\n\t\t<!-- 上传按钮区域 -->\n\t\t<view class=\"upload-section\" v-if=\"selectedImages.length > 0\">\n\t\t\t<button \n\t\t\t\tclass=\"upload-btn\" \n\t\t\t\t:class=\"{ 'uploading': isUploading }\"\n\t\t\t\t:disabled=\"isUploading\"\n\t\t\t\t@click=\"handleUpload\"\n\t\t\t>\n\t\t\t\t<text v-if=\"isUploading\" class=\"upload-text\">\n\t\t\t\t\t上传中... ({{ uploadProgress.current }}/{{ uploadProgress.total }})\n\t\t\t\t</text>\n\t\t\t\t<text v-else class=\"upload-text\">\n\t\t\t\t\t上传图片 ({{ selectedImages.length }})\n\t\t\t\t</text>\n\t\t\t</button>\n\t\t</view>\n\n\t\t<!-- 上传进度弹窗 -->\n\t\t<view class=\"upload-modal\" v-if=\"isUploading\">\n\t\t\t<view class=\"upload-content\">\n\t\t\t\t<view class=\"upload-header\">\n\t\t\t\t\t<text class=\"upload-title\">正在上传图片</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"progress-section\">\n\t\t\t\t\t<view class=\"progress-bar\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"progress-fill\" \n\t\t\t\t\t\t\t:style=\"{ width: progressPercentage + '%' }\"\n\t\t\t\t\t\t></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"progress-text\">\n\t\t\t\t\t\t{{ uploadProgress.current }}/{{ uploadProgress.total }} \n\t\t\t\t\t\t({{ progressPercentage }}%)\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"upload-status\">\n\t\t\t\t\t<text class=\"status-text\">{{ uploadStatusText }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { showSuccess, showError } from '../../utils/helpers.js';\n\timport userManager from '../../utils/userManager.js';\n\timport urlConfig from '../../utils/urlConfig.js';\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\torderNumber: '',\n\t\t\t\tuserInfo: {},\n\t\t\t\tselectedImages: [],\n\t\t\t\tisUploading: false,\n\t\t\t\tuploadProgress: {\n\t\t\t\t\tcurrent: 0,\n\t\t\t\t\ttotal: 0\n\t\t\t\t},\n\t\t\t\tuploadStatusText: ''\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tprogressPercentage() {\n\t\t\t\tif (this.uploadProgress.total === 0) return 0;\n\t\t\t\treturn Math.round((this.uploadProgress.current / this.uploadProgress.total) * 100);\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t/**\n\t\t\t * 拍照\n\t\t\t */\n\t\t\ttakePhoto() {\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 9 - this.selectedImages.length, // 最多9张\n\t\t\t\t\tsizeType: ['original', 'compressed'],\n\t\t\t\t\tsourceType: ['camera'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tthis.addImages(res.tempFilePaths);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('拍照失败:', err);\n\t\t\t\t\t\tshowError('拍照失败');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 从相册选择\n\t\t\t */\n\t\t\tchooseFromAlbum() {\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 9 - this.selectedImages.length, // 最多9张\n\t\t\t\t\tsizeType: ['original', 'compressed'],\n\t\t\t\t\tsourceType: ['album'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tthis.addImages(res.tempFilePaths);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('选择图片失败:', err);\n\t\t\t\t\t\tshowError('选择图片失败');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 添加图片到选择列表\n\t\t\t */\n\t\t\taddImages(imagePaths) {\n\t\t\t\timagePaths.forEach(path => {\n\t\t\t\t\tif (this.selectedImages.length < 9) {\n\t\t\t\t\t\tthis.selectedImages.push({\n\t\t\t\t\t\t\tpath: path,\n\t\t\t\t\t\t\tname: `image_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tif (this.selectedImages.length >= 9) {\n\t\t\t\t\tshowError('最多只能选择9张图片');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 移除图片\n\t\t\t */\n\t\t\tremoveImage(index) {\n\t\t\t\tthis.selectedImages.splice(index, 1);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 处理上传\n\t\t\t */\n\t\t\tasync handleUpload() {\n\t\t\t\tif (this.selectedImages.length === 0) {\n\t\t\t\t\tshowError('请先选择图片');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tthis.isUploading = true;\n\t\t\t\tthis.uploadProgress = {\n\t\t\t\t\tcurrent: 0,\n\t\t\t\t\ttotal: this.selectedImages.length\n\t\t\t\t};\n\n\t\t\t\ttry {\n\t\t\t\t\tfor (let i = 0; i < this.selectedImages.length; i++) {\n\t\t\t\t\t\tconst image = this.selectedImages[i];\n\t\t\t\t\t\tthis.uploadStatusText = `正在上传第 ${i + 1} 张图片...`;\n\t\t\t\t\t\t\n\t\t\t\t\t\tawait this.uploadSingleImage(image);\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.uploadProgress.current = i + 1;\n\t\t\t\t\t}\n\n\t\t\t\t\tshowSuccess(`成功上传 ${this.selectedImages.length} 张图片`);\n\n\t\t\t\t\t// 发送事件通知图片管理页面刷新\n\t\t\t\t\tuni.$emit('imageUploaded', {\n\t\t\t\t\t\torderNumber: this.orderNumber,\n\t\t\t\t\t\tcount: this.selectedImages.length\n\t\t\t\t\t});\n\n\t\t\t\t\t// 上传完成后返回图片管理页面\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t}, 1500);\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('上传失败:', error);\n\t\t\t\t\tshowError('上传失败：' + error.message);\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isUploading = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 上传单张图片\n\t\t\t */\n\t\t\tasync uploadSingleImage(image) {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tconst authHeaders = userManager.getAuthHeaders();\n\n\t\t\t\t\tuni.uploadFile({\n\t\t\t\t\t\turl: urlConfig.getUploadUrl(),\n\t\t\t\t\t\tfilePath: image.path,\n\t\t\t\t\t\tname: 'file',\n\t\t\t\t\t\tformData: {\n\t\t\t\t\t\t\torder_number: this.orderNumber,\n\t\t\t\t\t\t\tfactory_name: this.userInfo.factory_name\n\t\t\t\t\t\t},\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Authorization': authHeaders.Authorization\n\t\t\t\t\t\t},\n\t\t\t\t\t\tsuccess: (uploadRes) => {\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tif (uploadRes.statusCode !== 200) {\n\t\t\t\t\t\t\t\t\treject(new Error(`服务器错误 (${uploadRes.statusCode})`));\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tconst result = JSON.parse(uploadRes.data);\n\t\t\t\t\t\t\t\tif (result.success) {\n\t\t\t\t\t\t\t\t\tresolve(result);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\treject(new Error(result.message || '上传失败'));\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} catch (parseError) {\n\t\t\t\t\t\t\t\tconsole.error('解析响应失败:', parseError, uploadRes.data);\n\t\t\t\t\t\t\t\treject(new Error('服务器响应格式错误'));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('上传请求失败:', err);\n\t\t\t\t\t\t\treject(new Error('网络连接失败'));\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 初始化\n\t\t\t */\n\t\t\tinit() {\n\t\t\t\t// 使用用户管理器检查登录状态\n\t\t\t\tif (userManager.requireLogin()) {\n\t\t\t\t\tthis.userInfo = userManager.getUserInfo();\n\t\t\t\t}\n\t\t\t\t// 如果未登录，requireLogin会自动跳转到登录页面\n\t\t\t}\n\t\t},\n\n\t\tonLoad(options) {\n\t\t\tthis.orderNumber = options.orderNumber || '';\n\t\t\tif (!this.orderNumber) {\n\t\t\t\tshowError('订单号不能为空');\n\t\t\t\tuni.navigateBack();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.init();\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.camera-container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f5f5f5;\n\t\tpadding: 20rpx;\n\t}\n\n\t.header {\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);\n\t}\n\n\t.order-info {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.order-icon {\n\t\tfont-size: 50rpx;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.order-details {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.order-label {\n\t\tfont-size: 26rpx;\n\t\tcolor: rgba(255, 255, 255, 0.8);\n\t\tmargin-bottom: 8rpx;\n\t}\n\n\t.order-number {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #ffffff;\n\t}\n\n\t.camera-section {\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.section-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.title-icon {\n\t\tfont-size: 36rpx;\n\t\tmargin-right: 15rpx;\n\t\tcolor: #667eea;\n\t}\n\n\t.title-text {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.camera-options {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t}\n\n\t.option-card {\n\t\tflex: 1;\n\t\tbackground: #f8f9ff;\n\t\tborder: 2rpx solid #e0e6ff;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 40rpx 20rpx;\n\t\ttext-align: center;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.option-card:active {\n\t\ttransform: scale(0.98);\n\t\tbackground: #f0f4ff;\n\t}\n\n\t.option-icon {\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.icon {\n\t\tfont-size: 60rpx;\n\t}\n\n\t.option-text {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t\tmargin-bottom: 10rpx;\n\t\tdisplay: block;\n\t}\n\n\t.option-desc {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t\tdisplay: block;\n\t}\n\n\t.preview-section {\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.image-preview-list {\n\t\twhite-space: nowrap;\n\t}\n\n\t.preview-item {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\twidth: 150rpx;\n\t\theight: 150rpx;\n\t\tmargin-right: 20rpx;\n\t\tborder-radius: 12rpx;\n\t\toverflow: hidden;\n\t}\n\n\t.preview-image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.remove-btn {\n\t\tposition: absolute;\n\t\ttop: -10rpx;\n\t\tright: -10rpx;\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t\tbackground: #ff4444;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(255, 68, 68, 0.3);\n\t}\n\n\t.remove-icon {\n\t\tfont-size: 24rpx;\n\t\tcolor: #ffffff;\n\t\tfont-weight: bold;\n\t}\n\n\t.upload-section {\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.upload-btn {\n\t\twidth: 100%;\n\t\theight: 90rpx;\n\t\tbackground: linear-gradient(135deg, #4CAF50, #45a049);\n\t\tborder: none;\n\t\tborder-radius: 12rpx;\n\t\tcolor: #ffffff;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.upload-btn:not(:disabled):active {\n\t\ttransform: scale(0.98);\n\t}\n\n\t.upload-btn:disabled {\n\t\topacity: 0.6;\n\t\ttransform: none;\n\t}\n\n\t.upload-btn.uploading {\n\t\tbackground: #cccccc;\n\t}\n\n\t.upload-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 9999;\n\t}\n\n\t.upload-content {\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 60rpx 40rpx;\n\t\tmargin: 0 40rpx;\n\t\tmin-width: 500rpx;\n\t}\n\n\t.upload-header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t.upload-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.progress-section {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.progress-bar {\n\t\twidth: 100%;\n\t\theight: 12rpx;\n\t\tbackground: #f0f0f0;\n\t\tborder-radius: 6rpx;\n\t\toverflow: hidden;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.progress-fill {\n\t\theight: 100%;\n\t\tbackground: linear-gradient(135deg, #4CAF50, #45a049);\n\t\ttransition: width 0.3s ease;\n\t}\n\n\t.progress-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\ttext-align: center;\n\t\tdisplay: block;\n\t}\n\n\t.upload-status {\n\t\ttext-align: center;\n\t}\n\n\t.status-text {\n\t\tfont-size: 26rpx;\n\t\tcolor: #999999;\n\t}\n</style>\n", "import MiniProgramPage from 'D:/Desktop/Warehouse/front/pages/camera/camera.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "showError", "showSuccess", "userManager", "urlConfig"], "mappings": ";;;;;AAgHC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,aAAa;AAAA,MACb,UAAU,CAAE;AAAA,MACZ,gBAAgB,CAAE;AAAA,MAClB,aAAa;AAAA,MACb,gBAAgB;AAAA,QACf,SAAS;AAAA,QACT,OAAO;AAAA,MACP;AAAA,MACD,kBAAkB;AAAA,IACnB;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,qBAAqB;AACpB,UAAI,KAAK,eAAe,UAAU;AAAG,eAAO;AAC5C,aAAO,KAAK,MAAO,KAAK,eAAe,UAAU,KAAK,eAAe,QAAS,GAAG;AAAA,IAClF;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAIR,YAAY;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,IAAI,KAAK,eAAe;AAAA;AAAA,QAC/B,UAAU,CAAC,YAAY,YAAY;AAAA,QACnC,YAAY,CAAC,QAAQ;AAAA,QACrB,SAAS,CAAC,QAAQ;AACjB,eAAK,UAAU,IAAI,aAAa;AAAA,QAChC;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAA,MAAA,MAAA,SAAA,kCAAc,SAAS,GAAG;AAC1BC,wBAAS,UAAC,MAAM;AAAA,QACjB;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,kBAAkB;AACjBD,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,IAAI,KAAK,eAAe;AAAA;AAAA,QAC/B,UAAU,CAAC,YAAY,YAAY;AAAA,QACnC,YAAY,CAAC,OAAO;AAAA,QACpB,SAAS,CAAC,QAAQ;AACjB,eAAK,UAAU,IAAI,aAAa;AAAA,QAChC;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAc,MAAA,MAAA,SAAA,kCAAA,WAAW,GAAG;AAC5BC,wBAAS,UAAC,QAAQ;AAAA,QACnB;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,UAAU,YAAY;AACrB,iBAAW,QAAQ,UAAQ;AAC1B,YAAI,KAAK,eAAe,SAAS,GAAG;AACnC,eAAK,eAAe,KAAK;AAAA,YACxB;AAAA,YACA,MAAM,SAAS,KAAK,IAAK,CAAA,IAAI,KAAK,OAAQ,EAAC,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC,CAAC;AAAA,UACrE,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAED,UAAI,KAAK,eAAe,UAAU,GAAG;AACpCA,sBAAS,UAAC,YAAY;AAAA,MACvB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,YAAY,OAAO;AAClB,WAAK,eAAe,OAAO,OAAO,CAAC;AAAA,IACnC;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,eAAe;AACpB,UAAI,KAAK,eAAe,WAAW,GAAG;AACrCA,sBAAS,UAAC,QAAQ;AAClB;AAAA,MACD;AAEA,WAAK,cAAc;AACnB,WAAK,iBAAiB;AAAA,QACrB,SAAS;AAAA,QACT,OAAO,KAAK,eAAe;AAAA;AAG5B,UAAI;AACH,iBAAS,IAAI,GAAG,IAAI,KAAK,eAAe,QAAQ,KAAK;AACpD,gBAAM,QAAQ,KAAK,eAAe,CAAC;AACnC,eAAK,mBAAmB,SAAS,IAAI,CAAC;AAEtC,gBAAM,KAAK,kBAAkB,KAAK;AAElC,eAAK,eAAe,UAAU,IAAI;AAAA,QACnC;AAEAC,sBAAW,YAAC,QAAQ,KAAK,eAAe,MAAM,MAAM;AAGpDF,sBAAG,MAAC,MAAM,iBAAiB;AAAA,UAC1B,aAAa,KAAK;AAAA,UAClB,OAAO,KAAK,eAAe;AAAA,QAC5B,CAAC;AAGD,mBAAW,MAAM;AAChBA,wBAAG,MAAC,aAAY;AAAA,QAChB,GAAE,IAAI;AAAA,MAEN,SAAO,OAAO;AACfA,6EAAc,SAAS,KAAK;AAC5BC,sBAAAA,UAAU,UAAU,MAAM,OAAO;AAAA,MAClC,UAAU;AACT,aAAK,cAAc;AAAA,MACpB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,kBAAkB,OAAO;AAC9B,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,cAAM,cAAcE,8BAAY;AAEhCH,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAKI,gBAAS,UAAC,aAAc;AAAA,UAC7B,UAAU,MAAM;AAAA,UAChB,MAAM;AAAA,UACN,UAAU;AAAA,YACT,cAAc,KAAK;AAAA,YACnB,cAAc,KAAK,SAAS;AAAA,UAC5B;AAAA,UACD,QAAQ;AAAA,YACP,iBAAiB,YAAY;AAAA,UAC7B;AAAA,UACD,SAAS,CAAC,cAAc;AACvB,gBAAI;AACH,kBAAI,UAAU,eAAe,KAAK;AACjC,uBAAO,IAAI,MAAM,UAAU,UAAU,UAAU,GAAG,CAAC;AACnD;AAAA,cACD;AAEA,oBAAM,SAAS,KAAK,MAAM,UAAU,IAAI;AACxC,kBAAI,OAAO,SAAS;AACnB,wBAAQ,MAAM;AAAA,qBACR;AACN,uBAAO,IAAI,MAAM,OAAO,WAAW,MAAM,CAAC;AAAA,cAC3C;AAAA,YACD,SAAS,YAAY;AACpBJ,mFAAc,WAAW,YAAY,UAAU,IAAI;AACnD,qBAAO,IAAI,MAAM,WAAW,CAAC;AAAA,YAC9B;AAAA,UACA;AAAA,UACD,MAAM,CAAC,QAAQ;AACdA,0BAAA,MAAA,MAAA,SAAA,kCAAc,WAAW,GAAG;AAC5B,mBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,UAC3B;AAAA,QACD,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,OAAO;AAEN,UAAIG,kBAAAA,YAAY,gBAAgB;AAC/B,aAAK,WAAWA,8BAAY;MAC7B;AAAA,IAED;AAAA,EACA;AAAA,EAED,OAAO,SAAS;AACf,SAAK,cAAc,QAAQ,eAAe;AAC1C,QAAI,CAAC,KAAK,aAAa;AACtBF,oBAAS,UAAC,SAAS;AACnBD,oBAAG,MAAC,aAAY;AAChB;AAAA,IACD;AACA,SAAK,KAAI;AAAA,EACV;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChTD,GAAG,WAAW,eAAe;"}