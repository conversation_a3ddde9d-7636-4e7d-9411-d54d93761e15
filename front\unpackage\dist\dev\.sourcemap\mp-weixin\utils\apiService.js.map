{"version": 3, "file": "apiService.js", "sources": ["utils/apiService.js"], "sourcesContent": ["/**\n * API服务模块\n * 统一管理所有API调用逻辑\n */\n\n// 根据环境自动选择API基础URL\nconst getBaseUrl = () => {\n\t// 检查是否在微信小程序环境\n\t// #ifdef MP-WEIXIN\n\treturn 'https://www.mls2005.top';\n\t// #endif\n\n\t// 检查是否在H5环境\n\t// #ifdef H5\n\tif (window.location.protocol === 'https:') {\n\t\treturn 'https://www.mls2005.top';\n\t}\n\treturn 'http://localhost:3000';\n\t// #endif\n\n\t// 其他环境默认使用HTTPS\n\treturn 'https://www.mls2005.top';\n};\n\nconst API_CONFIG = {\n\tbaseUrl: getBaseUrl(),\n\ttimeout: 30000\n};\n\n/**\n * API服务类\n */\nclass ApiService {\n\tconstructor() {\n\t\tthis.baseUrl = API_CONFIG.baseUrl;\n\t\tthis.timeout = API_CONFIG.timeout;\n\t}\n\n\t/**\n\t * 检查服务器连接\n\t */\n\tasync checkServerConnection() {\n\t\tconst maxRetries = 3;\n\t\tconst retryDelay = 2000; // 2秒\n\n\t\tfor (let attempt = 1; attempt <= maxRetries; attempt++) {\n\t\t\ttry {\n\t\t\t\tconsole.log(`检查服务器连接 (尝试 ${attempt}/${maxRetries}):`, `${this.baseUrl}/api/health`);\n\t\t\t\tconst response = await uni.request({\n\t\t\t\t\turl: `${this.baseUrl}/api/health`,\n\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\ttimeout: 15000 // 增加到15秒\n\t\t\t\t});\n\t\t\t\tconsole.log('服务器连接检查结果:', response);\n\t\t\t\tif (response.statusCode === 200) {\n\t\t\t\t\tconsole.log('✅ 服务器连接正常');\n\t\t\t\t\treturn true;\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log('❌ 服务器连接失败，状态码:', response.statusCode);\n\t\t\t\t\tif (attempt === maxRetries) return false;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(`❌ 服务器连接检查失败 (尝试 ${attempt}/${maxRetries}):`, error);\n\n\t\t\t\t// 详细错误诊断\n\t\t\t\tif (error.errno) {\n\t\t\t\t\tconsole.error('错误代码:', error.errno);\n\t\t\t\t\tif (error.errno === 600001) {\n\t\t\t\t\t\tconsole.error('网络连接被拒绝 - 可能原因:');\n\t\t\t\t\t\tconsole.error('1. 服务器未启动');\n\t\t\t\t\t\tconsole.error('2. 端口被防火墙阻止');\n\t\t\t\t\t\tconsole.error('3. 服务器地址或端口错误');\n\t\t\t\t\t} else if (error.errno === 5) {\n\t\t\t\t\t\tconsole.error('请求超时 - 可能原因:');\n\t\t\t\t\t\tconsole.error('1. 网络连接缓慢');\n\t\t\t\t\t\tconsole.error('2. 服务器响应缓慢');\n\t\t\t\t\t\tconsole.error('3. 防火墙阻止连接');\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (attempt === maxRetries) return false;\n\n\t\t\t\t// 等待后重试\n\t\t\t\tif (attempt < maxRetries) {\n\t\t\t\t\tconsole.log(`等待 ${retryDelay/1000} 秒后重试...`);\n\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, retryDelay));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * 通用图片识别\n\t * @param {string} imagePath 图片路径\n\t * @param {string} username 用户名（可选）\n\t */\n\tasync recognizeImage(imagePath, username = null) {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tconst timeout = setTimeout(() => {\n\t\t\t\treject(new Error('请求超时，请检查网络连接'));\n\t\t\t}, this.timeout);\n\n\t\t\t// 构建表单数据\n\t\t\tconst formData = {};\n\t\t\tif (username) {\n\t\t\t\tformData.username = username;\n\t\t\t}\n\n\t\t\tuni.uploadFile({\n\t\t\t\turl: `${this.baseUrl}/api/recognize`,\n\t\t\t\tfilePath: imagePath,\n\t\t\t\tname: 'image',\n\t\t\t\tformData: formData,\n\t\t\t\tsuccess: (uploadRes) => {\n\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\ttry {\n\t\t\t\t\t\tif (uploadRes.statusCode !== 200) {\n\t\t\t\t\t\t\treject(new Error(`服务器错误 (${uploadRes.statusCode})`));\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst result = JSON.parse(uploadRes.data);\n\t\t\t\t\t\tif (result.success) {\n\t\t\t\t\t\t\tresolve(result);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\treject(new Error(result.message || '识别失败'));\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (parseError) {\n\t\t\t\t\t\tconsole.error('解析响应失败:', parseError, uploadRes.data);\n\t\t\t\t\t\treject(new Error('服务器响应格式错误'));\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: (error) => {\n\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\tconsole.error('上传失败:', error);\n\n\t\t\t\t\tlet errorMessage = '上传失败';\n\t\t\t\t\tif (error.errMsg) {\n\t\t\t\t\t\tif (error.errMsg.includes('timeout')) {\n\t\t\t\t\t\t\terrorMessage = '请求超时，请检查网络连接';\n\t\t\t\t\t\t} else if (error.errMsg.includes('fail')) {\n\t\t\t\t\t\t\terrorMessage = '网络连接失败，请检查服务器是否启动';\n\t\t\t\t\t\t} else if (error.errMsg.includes('boundary')) {\n\t\t\t\t\t\t\terrorMessage = '文件上传格式错误，请重试';\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\terrorMessage = error.errMsg;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treject(new Error(errorMessage));\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t}\n\n\t/**\n\t * 批量图片识别（通用文字识别）\n\t * @param {Array} imagePaths 图片路径数组\n\t * @param {Function} progressCallback 进度回调函数\n\t */\n\tasync recognizeMultipleImages(imagePaths, progressCallback) {\n\t\tconst results = [];\n\n\t\tfor (let i = 0; i < imagePaths.length; i++) {\n\t\t\ttry {\n\t\t\t\t// 调用进度回调\n\t\t\t\tif (progressCallback) {\n\t\t\t\t\tprogressCallback(i + 1, imagePaths.length);\n\t\t\t\t}\n\n\t\t\t\tconst result = await this.recognizeImage(imagePaths[i]);\n\t\t\t\tresults.push({\n\t\t\t\t\tindex: i,\n\t\t\t\t\timagePath: imagePaths[i],\n\t\t\t\t\tsuccess: true,\n\t\t\t\t\tresult: result\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tresults.push({\n\t\t\t\t\tindex: i,\n\t\t\t\t\timagePath: imagePaths[i],\n\t\t\t\t\tsuccess: false,\n\t\t\t\t\terror: error.message\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\treturn results;\n\t}\n\n\t/**\n\t * 装柜放船样登记表识别\n\t * @param {string} imagePath 图片路径\n\t */\n\tasync recognizeRegistrationForm(imagePath) {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tconst timeout = setTimeout(() => {\n\t\t\t\treject(new Error('装柜放船样登记表识别超时'));\n\t\t\t}, this.timeout);\n\n\t\t\tuni.uploadFile({\n\t\t\t\turl: `${this.baseUrl}/api/registration-form/recognize`,\n\t\t\t\tfilePath: imagePath,\n\t\t\t\tname: 'image',\n\t\t\t\tformData: {},\n\t\t\t\tsuccess: (uploadRes) => {\n\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\ttry {\n\t\t\t\t\t\tif (uploadRes.statusCode !== 200) {\n\t\t\t\t\t\t\tresolve({ isRegistrationForm: false });\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst result = JSON.parse(uploadRes.data);\n\t\t\t\t\t\tif (result.success && result.isRegistrationForm) {\n\t\t\t\t\t\t\tresolve(result);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tresolve({ isRegistrationForm: false });\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (parseError) {\n\t\t\t\t\t\tconsole.error('解析装柜放船样登记表响应失败:', parseError);\n\t\t\t\t\t\tresolve({ isRegistrationForm: false });\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: (error) => {\n\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\tconsole.log('装柜放船样登记表识别失败，使用普通识别:', error);\n\t\t\t\t\tresolve({ isRegistrationForm: false });\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t}\n\n\t/**\n\t * 装柜信息表识别\n\t * @param {string} imagePath 图片路径\n\t */\n\tasync recognizeCartonInfo(imagePath) {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tconst timeout = setTimeout(() => {\n\t\t\t\treject(new Error('装柜信息表识别超时'));\n\t\t\t}, this.timeout);\n\n\t\t\tuni.uploadFile({\n\t\t\t\turl: `${this.baseUrl}/api/carton-info/recognize`,\n\t\t\t\tfilePath: imagePath,\n\t\t\t\tname: 'image',\n\t\t\t\tformData: {},\n\t\t\t\tsuccess: (uploadRes) => {\n\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\ttry {\n\t\t\t\t\t\tif (uploadRes.statusCode !== 200) {\n\t\t\t\t\t\t\tresolve({ isCartonInfo: false });\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst result = JSON.parse(uploadRes.data);\n\t\t\t\t\t\tif (result.success && result.isCartonInfo) {\n\t\t\t\t\t\t\tresolve(result);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tresolve({ isCartonInfo: false });\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (parseError) {\n\t\t\t\t\t\tconsole.error('解析装柜信息表响应失败:', parseError);\n\t\t\t\t\t\tresolve({ isCartonInfo: false });\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: (error) => {\n\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\tconsole.log('装柜信息表识别失败，使用普通识别:', error);\n\t\t\t\t\tresolve({ isCartonInfo: false });\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t}\n\n\t/**\n\t * 保存装柜放船样登记表数据\n\t * @param {Array} data 表格数据\n\t * @param {Object} imageInfo 图片信息\n\t */\n\tasync saveRegistrationFormData(data, imageInfo) {\n\t\tconst dataWithUploadTime = data.map(row => ({\n\t\t\t...row,\n\t\t\tUploadDate: new Date().toISOString()\n\t\t}));\n\n\t\tconst response = await uni.request({\n\t\t\turl: `${this.baseUrl}/api/registration-form/save`,\n\t\t\tmethod: 'POST',\n\t\t\tdata: {\n\t\t\t\tdata: dataWithUploadTime,\n\t\t\t\timageInfo: imageInfo,\n\t\t\t\tuploadTime: new Date().toISOString()\n\t\t\t},\n\t\t\theader: {\n\t\t\t\t'Content-Type': 'application/json'\n\t\t\t}\n\t\t});\n\n\t\tif (response.statusCode === 200 && response.data.success) {\n\t\t\treturn response.data;\n\t\t} else {\n\t\t\tthrow new Error(response.data.message || '保存失败');\n\t\t}\n\t}\n\n\t/**\n\t * 保存装柜信息表数据\n\t * @param {Object} data 装柜信息数据\n\t * @param {Object} imageInfo 图片信息\n\t */\n\tasync saveCartonInfoData(data, imageInfo) {\n\t\tconst dataWithUploadTime = {\n\t\t\t...data,\n\t\t\tUploadDate: new Date().toISOString()\n\t\t};\n\n\t\tconst response = await uni.request({\n\t\t\turl: `${this.baseUrl}/api/carton-info/save`,\n\t\t\tmethod: 'POST',\n\t\t\tdata: {\n\t\t\t\tdata: dataWithUploadTime,\n\t\t\t\timageInfo: imageInfo,\n\t\t\t\tuploadTime: new Date().toISOString()\n\t\t\t},\n\t\t\theader: {\n\t\t\t\t'Content-Type': 'application/json'\n\t\t\t}\n\t\t});\n\n\t\tif (response.statusCode === 200 && response.data.success) {\n\t\t\treturn response.data;\n\t\t} else {\n\t\t\tthrow new Error(response.data.message || '保存失败');\n\t\t}\n\t}\n\n\t/**\n\t * 获取今日历史记录\n\t * @param {string} username - 用户名（可选）\n\t */\n\tasync getTodayHistory(username = null) {\n\t\ttry {\n\t\t\tconsole.log('📋 请求今日历史记录...');\n\n\t\t\t// 构建URL，如果有用户名则添加查询参数\n\t\t\tlet url = `${this.baseUrl}/api/history/today`;\n\t\t\tif (username) {\n\t\t\t\turl += `?username=${encodeURIComponent(username)}`;\n\t\t\t\tconsole.log(`📋 查询用户 ${username} 的历史记录`);\n\t\t\t}\n\n\t\t\tconst response = await uni.request({\n\t\t\t\turl: url,\n\t\t\t\tmethod: 'GET',\n\t\t\t\theader: {\n\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t},\n\t\t\t\ttimeout: this.timeout\n\t\t\t});\n\n\t\t\tconsole.log('📋 历史记录响应:', response);\n\n\t\t\tif (response.statusCode === 200 && response.data.success) {\n\t\t\t\treturn response.data;\n\t\t\t} else {\n\t\t\t\tthrow new Error(response.data.message || '获取历史记录失败');\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('❌ 获取今日历史记录失败:', error);\n\t\t\tthrow new Error('获取历史记录失败: ' + error.message);\n\t\t}\n\t}\n\n}\n\n// 创建单例实例\nconst apiService = new ApiService();\n\nexport default apiService;\n"], "names": ["uni"], "mappings": ";;AAMA,MAAM,aAAa,MAAM;AAGxB,SAAO;AAaR;AAEA,MAAM,aAAa;AAAA,EAClB,SAAS,WAAY;AAAA,EACrB,SAAS;AACV;AAKA,MAAM,WAAW;AAAA,EAChB,cAAc;AACb,SAAK,UAAU,WAAW;AAC1B,SAAK,UAAU,WAAW;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,wBAAwB;AAC7B,UAAM,aAAa;AACnB,UAAM,aAAa;AAEnB,aAAS,UAAU,GAAG,WAAW,YAAY,WAAW;AACvD,UAAI;AACHA,sBAAA,MAAA,MAAA,OAAA,6BAAY,eAAe,OAAO,IAAI,UAAU,MAAM,GAAG,KAAK,OAAO,aAAa;AAClF,cAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,UAClC,KAAK,GAAG,KAAK,OAAO;AAAA,UACpB,QAAQ;AAAA,UACR,SAAS;AAAA;AAAA,QACd,CAAK;AACDA,sBAAY,MAAA,MAAA,OAAA,6BAAA,cAAc,QAAQ;AAClC,YAAI,SAAS,eAAe,KAAK;AAChCA,wBAAAA,MAAA,MAAA,OAAA,6BAAY,WAAW;AACvB,iBAAO;AAAA,QACZ,OAAW;AACNA,wBAAY,MAAA,MAAA,OAAA,6BAAA,kBAAkB,SAAS,UAAU;AACjD,cAAI,YAAY;AAAY,mBAAO;AAAA,QACnC;AAAA,MACD,SAAQ,OAAO;AACfA,sBAAAA,MAAc,MAAA,SAAA,6BAAA,mBAAmB,OAAO,IAAI,UAAU,MAAM,KAAK;AAGjE,YAAI,MAAM,OAAO;AAChBA,wBAAA,MAAA,MAAA,SAAA,6BAAc,SAAS,MAAM,KAAK;AAClC,cAAI,MAAM,UAAU,QAAQ;AAC3BA,0BAAAA,kDAAc,iBAAiB;AAC/BA,0BAAAA,MAAA,MAAA,SAAA,6BAAc,WAAW;AACzBA,0BAAAA,MAAc,MAAA,SAAA,6BAAA,aAAa;AAC3BA,0BAAAA,MAAc,MAAA,SAAA,6BAAA,eAAe;AAAA,UACnC,WAAgB,MAAM,UAAU,GAAG;AAC7BA,0BAAAA,MAAc,MAAA,SAAA,6BAAA,cAAc;AAC5BA,0BAAAA,MAAA,MAAA,SAAA,6BAAc,WAAW;AACzBA,0BAAAA,MAAA,MAAA,SAAA,6BAAc,YAAY;AAC1BA,0BAAAA,MAAA,MAAA,SAAA,6BAAc,YAAY;AAAA,UAC1B;AAAA,QACD;AAED,YAAI,YAAY;AAAY,iBAAO;AAGnC,YAAI,UAAU,YAAY;AACzBA,8BAAA,MAAA,OAAA,6BAAY,MAAM,aAAW,GAAI,UAAU;AAC3C,gBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,UAAU,CAAC;AAAA,QAC5D;AAAA,MACD;AAAA,IACD;AACD,WAAO;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,eAAe,WAAW,WAAW,MAAM;AAChD,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,YAAM,UAAU,WAAW,MAAM;AAChC,eAAO,IAAI,MAAM,cAAc,CAAC;AAAA,MACpC,GAAM,KAAK,OAAO;AAGf,YAAM,WAAW,CAAA;AACjB,UAAI,UAAU;AACb,iBAAS,WAAW;AAAA,MACpB;AAEDA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,GAAG,KAAK,OAAO;AAAA,QACpB,UAAU;AAAA,QACV,MAAM;AAAA,QACN;AAAA,QACA,SAAS,CAAC,cAAc;AACvB,uBAAa,OAAO;AACpB,cAAI;AACH,gBAAI,UAAU,eAAe,KAAK;AACjC,qBAAO,IAAI,MAAM,UAAU,UAAU,UAAU,GAAG,CAAC;AACnD;AAAA,YACA;AAED,kBAAM,SAAS,KAAK,MAAM,UAAU,IAAI;AACxC,gBAAI,OAAO,SAAS;AACnB,sBAAQ,MAAM;AAAA,YACrB,OAAa;AACN,qBAAO,IAAI,MAAM,OAAO,WAAW,MAAM,CAAC;AAAA,YAC1C;AAAA,UACD,SAAQ,YAAY;AACpBA,gCAAc,MAAA,SAAA,8BAAA,WAAW,YAAY,UAAU,IAAI;AACnD,mBAAO,IAAI,MAAM,WAAW,CAAC;AAAA,UAC7B;AAAA,QACD;AAAA,QACD,MAAM,CAAC,UAAU;AAChB,uBAAa,OAAO;AACpBA,2EAAc,SAAS,KAAK;AAE5B,cAAI,eAAe;AACnB,cAAI,MAAM,QAAQ;AACjB,gBAAI,MAAM,OAAO,SAAS,SAAS,GAAG;AACrC,6BAAe;AAAA,YACf,WAAU,MAAM,OAAO,SAAS,MAAM,GAAG;AACzC,6BAAe;AAAA,YACf,WAAU,MAAM,OAAO,SAAS,UAAU,GAAG;AAC7C,6BAAe;AAAA,YACtB,OAAa;AACN,6BAAe,MAAM;AAAA,YACrB;AAAA,UACD;AAED,iBAAO,IAAI,MAAM,YAAY,CAAC;AAAA,QAC9B;AAAA,MACL,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,wBAAwB,YAAY,kBAAkB;AAC3D,UAAM,UAAU,CAAA;AAEhB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC3C,UAAI;AAEH,YAAI,kBAAkB;AACrB,2BAAiB,IAAI,GAAG,WAAW,MAAM;AAAA,QACzC;AAED,cAAM,SAAS,MAAM,KAAK,eAAe,WAAW,CAAC,CAAC;AACtD,gBAAQ,KAAK;AAAA,UACZ,OAAO;AAAA,UACP,WAAW,WAAW,CAAC;AAAA,UACvB,SAAS;AAAA,UACT;AAAA,QACL,CAAK;AAAA,MACD,SAAQ,OAAO;AACf,gBAAQ,KAAK;AAAA,UACZ,OAAO;AAAA,UACP,WAAW,WAAW,CAAC;AAAA,UACvB,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QAClB,CAAK;AAAA,MACD;AAAA,IACD;AAED,WAAO;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,0BAA0B,WAAW;AAC1C,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,YAAM,UAAU,WAAW,MAAM;AAChC,eAAO,IAAI,MAAM,cAAc,CAAC;AAAA,MACpC,GAAM,KAAK,OAAO;AAEfA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,GAAG,KAAK,OAAO;AAAA,QACpB,UAAU;AAAA,QACV,MAAM;AAAA,QACN,UAAU,CAAE;AAAA,QACZ,SAAS,CAAC,cAAc;AACvB,uBAAa,OAAO;AACpB,cAAI;AACH,gBAAI,UAAU,eAAe,KAAK;AACjC,sBAAQ,EAAE,oBAAoB,MAAK,CAAE;AACrC;AAAA,YACA;AAED,kBAAM,SAAS,KAAK,MAAM,UAAU,IAAI;AACxC,gBAAI,OAAO,WAAW,OAAO,oBAAoB;AAChD,sBAAQ,MAAM;AAAA,YACrB,OAAa;AACN,sBAAQ,EAAE,oBAAoB,MAAK,CAAE;AAAA,YACrC;AAAA,UACD,SAAQ,YAAY;AACpBA,6EAAc,mBAAmB,UAAU;AAC3C,oBAAQ,EAAE,oBAAoB,MAAK,CAAE;AAAA,UACrC;AAAA,QACD;AAAA,QACD,MAAM,CAAC,UAAU;AAChB,uBAAa,OAAO;AACpBA,yEAAY,wBAAwB,KAAK;AACzC,kBAAQ,EAAE,oBAAoB,MAAK,CAAE;AAAA,QACrC;AAAA,MACL,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,oBAAoB,WAAW;AACpC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,YAAM,UAAU,WAAW,MAAM;AAChC,eAAO,IAAI,MAAM,WAAW,CAAC;AAAA,MACjC,GAAM,KAAK,OAAO;AAEfA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,GAAG,KAAK,OAAO;AAAA,QACpB,UAAU;AAAA,QACV,MAAM;AAAA,QACN,UAAU,CAAE;AAAA,QACZ,SAAS,CAAC,cAAc;AACvB,uBAAa,OAAO;AACpB,cAAI;AACH,gBAAI,UAAU,eAAe,KAAK;AACjC,sBAAQ,EAAE,cAAc,MAAK,CAAE;AAC/B;AAAA,YACA;AAED,kBAAM,SAAS,KAAK,MAAM,UAAU,IAAI;AACxC,gBAAI,OAAO,WAAW,OAAO,cAAc;AAC1C,sBAAQ,MAAM;AAAA,YACrB,OAAa;AACN,sBAAQ,EAAE,cAAc,MAAK,CAAE;AAAA,YAC/B;AAAA,UACD,SAAQ,YAAY;AACpBA,0BAAc,MAAA,MAAA,SAAA,8BAAA,gBAAgB,UAAU;AACxC,oBAAQ,EAAE,cAAc,MAAK,CAAE;AAAA,UAC/B;AAAA,QACD;AAAA,QACD,MAAM,CAAC,UAAU;AAChB,uBAAa,OAAO;AACpBA,yEAAY,qBAAqB,KAAK;AACtC,kBAAQ,EAAE,cAAc,MAAK,CAAE;AAAA,QAC/B;AAAA,MACL,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,yBAAyB,MAAM,WAAW;AAC/C,UAAM,qBAAqB,KAAK,IAAI,UAAQ;AAAA,MAC3C,GAAG;AAAA,MACH,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,IACpC,EAAC;AAEF,UAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,MAClC,KAAK,GAAG,KAAK,OAAO;AAAA,MACpB,QAAQ;AAAA,MACR,MAAM;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,MACpC;AAAA,MACD,QAAQ;AAAA,QACP,gBAAgB;AAAA,MAChB;AAAA,IACJ,CAAG;AAED,QAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS;AACzD,aAAO,SAAS;AAAA,IACnB,OAAS;AACN,YAAM,IAAI,MAAM,SAAS,KAAK,WAAW,MAAM;AAAA,IAC/C;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,mBAAmB,MAAM,WAAW;AACzC,UAAM,qBAAqB;AAAA,MAC1B,GAAG;AAAA,MACH,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,IACvC;AAEE,UAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,MAClC,KAAK,GAAG,KAAK,OAAO;AAAA,MACpB,QAAQ;AAAA,MACR,MAAM;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,MACpC;AAAA,MACD,QAAQ;AAAA,QACP,gBAAgB;AAAA,MAChB;AAAA,IACJ,CAAG;AAED,QAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS;AACzD,aAAO,SAAS;AAAA,IACnB,OAAS;AACN,YAAM,IAAI,MAAM,SAAS,KAAK,WAAW,MAAM;AAAA,IAC/C;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,gBAAgB,WAAW,MAAM;AACtC,QAAI;AACHA,oBAAAA,MAAY,MAAA,OAAA,8BAAA,gBAAgB;AAG5B,UAAI,MAAM,GAAG,KAAK,OAAO;AACzB,UAAI,UAAU;AACb,eAAO,aAAa,mBAAmB,QAAQ,CAAC;AAChDA,4BAAA,MAAA,OAAA,8BAAY,WAAW,QAAQ,QAAQ;AAAA,MACvC;AAED,YAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,QAClC;AAAA,QACA,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP,gBAAgB;AAAA,QAChB;AAAA,QACD,SAAS,KAAK;AAAA,MAClB,CAAI;AAEDA,oBAAA,MAAA,MAAA,OAAA,8BAAY,cAAc,QAAQ;AAElC,UAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS;AACzD,eAAO,SAAS;AAAA,MACpB,OAAU;AACN,cAAM,IAAI,MAAM,SAAS,KAAK,WAAW,UAAU;AAAA,MACnD;AAAA,IACD,SAAQ,OAAO;AACfA,oBAAA,MAAA,MAAA,SAAA,8BAAc,iBAAiB,KAAK;AACpC,YAAM,IAAI,MAAM,eAAe,MAAM,OAAO;AAAA,IAC5C;AAAA,EACD;AAEF;AAGK,MAAC,aAAa,IAAI,WAAU;;"}