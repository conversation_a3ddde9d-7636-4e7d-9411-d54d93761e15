<template>
	<view class="camera-container">
		<!-- 头部信息 -->
		<view class="header">
			<view class="order-info">
				<text class="order-icon">📦</text>
				<view class="order-details">
					<text class="order-label">订单号</text>
					<text class="order-number">{{ orderNumber }}</text>
				</view>
			</view>
		</view>

		<!-- 拍照选择区域 -->
		<view class="camera-section">
			<view class="section-title">
				<text class="title-icon">📷</text>
				<text class="title-text">选择图片</text>
			</view>

			<view class="camera-options">
				<view class="option-card" @click="takePhoto">
					<view class="option-icon">
						<text class="icon">📸</text>
					</view>
					<text class="option-text">拍照</text>
					<text class="option-desc">使用相机拍摄新照片</text>
				</view>

				<view class="option-card" @click="chooseFromAlbum">
					<view class="option-icon">
						<text class="icon">🖼️</text>
					</view>
					<text class="option-text">相册</text>
					<text class="option-desc">从相册选择图片</text>
				</view>
			</view>
		</view>

		<!-- 已选择图片预览 -->
		<view class="preview-section" v-if="selectedImages.length > 0">
			<view class="section-title">
				<text class="title-icon">🖼️</text>
				<text class="title-text">已选择图片 ({{ selectedImages.length }})</text>
			</view>

			<scroll-view class="image-preview-list" scroll-x="true">
				<view 
					class="preview-item" 
					v-for="(image, index) in selectedImages" 
					:key="index"
				>
					<image 
						class="preview-image" 
						:src="image.path" 
						mode="aspectFill"
					/>
					<view class="remove-btn" @click="removeImage(index)">
						<text class="remove-icon">✕</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 上传按钮区域 -->
		<view class="upload-section" v-if="selectedImages.length > 0">
			<button 
				class="upload-btn" 
				:class="{ 'uploading': isUploading }"
				:disabled="isUploading"
				@click="handleUpload"
			>
				<text v-if="isUploading" class="upload-text">
					上传中... ({{ uploadProgress.current }}/{{ uploadProgress.total }})
				</text>
				<text v-else class="upload-text">
					上传图片 ({{ selectedImages.length }})
				</text>
			</button>
		</view>

		<!-- 上传进度弹窗 -->
		<view class="upload-modal" v-if="isUploading">
			<view class="upload-content">
				<view class="upload-header">
					<text class="upload-title">正在上传图片</text>
				</view>
				<view class="progress-section">
					<view class="progress-bar">
						<view 
							class="progress-fill" 
							:style="{ width: progressPercentage + '%' }"
						></view>
					</view>
					<text class="progress-text">
						{{ uploadProgress.current }}/{{ uploadProgress.total }} 
						({{ progressPercentage }}%)
					</text>
				</view>
				<view class="upload-status">
					<text class="status-text">{{ uploadStatusText }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { showSuccess, showError } from '../../utils/helpers.js';
	import userManager from '../../utils/userManager.js';
	import urlConfig from '../../utils/urlConfig.js';

	export default {
		data() {
			return {
				orderNumber: '',
				userInfo: {},
				selectedImages: [],
				isUploading: false,
				uploadProgress: {
					current: 0,
					total: 0
				},
				uploadStatusText: ''
			}
		},
		computed: {
			progressPercentage() {
				if (this.uploadProgress.total === 0) return 0;
				return Math.round((this.uploadProgress.current / this.uploadProgress.total) * 100);
			}
		},
		methods: {
			/**
			 * 拍照
			 */
			takePhoto() {
				uni.chooseImage({
					count: 9 - this.selectedImages.length, // 最多9张
					sizeType: ['original', 'compressed'],
					sourceType: ['camera'],
					success: (res) => {
						this.addImages(res.tempFilePaths);
					},
					fail: (err) => {
						console.error('拍照失败:', err);
						showError('拍照失败');
					}
				});
			},

			/**
			 * 从相册选择
			 */
			chooseFromAlbum() {
				uni.chooseImage({
					count: 9 - this.selectedImages.length, // 最多9张
					sizeType: ['original', 'compressed'],
					sourceType: ['album'],
					success: (res) => {
						this.addImages(res.tempFilePaths);
					},
					fail: (err) => {
						console.error('选择图片失败:', err);
						showError('选择图片失败');
					}
				});
			},

			/**
			 * 添加图片到选择列表
			 */
			addImages(imagePaths) {
				imagePaths.forEach(path => {
					if (this.selectedImages.length < 9) {
						this.selectedImages.push({
							path: path,
							name: `image_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`
						});
					}
				});

				if (this.selectedImages.length >= 9) {
					showError('最多只能选择9张图片');
				}
			},

			/**
			 * 移除图片
			 */
			removeImage(index) {
				this.selectedImages.splice(index, 1);
			},

			/**
			 * 处理上传
			 */
			async handleUpload() {
				if (this.selectedImages.length === 0) {
					showError('请先选择图片');
					return;
				}

				this.isUploading = true;
				this.uploadProgress = {
					current: 0,
					total: this.selectedImages.length
				};

				try {
					for (let i = 0; i < this.selectedImages.length; i++) {
						const image = this.selectedImages[i];
						this.uploadStatusText = `正在上传第 ${i + 1} 张图片...`;
						
						await this.uploadSingleImage(image);
						
						this.uploadProgress.current = i + 1;
					}

					showSuccess(`成功上传 ${this.selectedImages.length} 张图片`);

					// 发送事件通知图片管理页面刷新
					uni.$emit('imageUploaded', {
						orderNumber: this.orderNumber,
						count: this.selectedImages.length
					});

					// 上传完成后返回图片管理页面
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);

				} catch (error) {
					console.error('上传失败:', error);
					showError('上传失败：' + error.message);
				} finally {
					this.isUploading = false;
				}
			},

			/**
			 * 上传单张图片
			 */
			async uploadSingleImage(image) {
				return new Promise((resolve, reject) => {
					const authHeaders = userManager.getAuthHeaders();

					uni.uploadFile({
						url: urlConfig.getUploadUrl(),
						filePath: image.path,
						name: 'file',
						formData: {
							order_number: this.orderNumber,
							factory_name: this.userInfo.factory_name
						},
						header: {
							'Authorization': authHeaders.Authorization
						},
						success: (uploadRes) => {
							try {
								if (uploadRes.statusCode !== 200) {
									reject(new Error(`服务器错误 (${uploadRes.statusCode})`));
									return;
								}

								const result = JSON.parse(uploadRes.data);
								if (result.success) {
									resolve(result);
								} else {
									reject(new Error(result.message || '上传失败'));
								}
							} catch (parseError) {
								console.error('解析响应失败:', parseError, uploadRes.data);
								reject(new Error('服务器响应格式错误'));
							}
						},
						fail: (err) => {
							console.error('上传请求失败:', err);
							reject(new Error('网络连接失败'));
						}
					});
				});
			},

			/**
			 * 初始化
			 */
			init() {
				// 使用用户管理器检查登录状态
				if (userManager.requireLogin()) {
					this.userInfo = userManager.getUserInfo();
				}
				// 如果未登录，requireLogin会自动跳转到登录页面
			}
		},

		onLoad(options) {
			this.orderNumber = options.orderNumber || '';
			if (!this.orderNumber) {
				showError('订单号不能为空');
				uni.navigateBack();
				return;
			}
			this.init();
		}
	}
</script>

<style scoped>
	.camera-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
	}

	.header {
		background: linear-gradient(135deg, #667eea, #764ba2);
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
	}

	.order-info {
		display: flex;
		align-items: center;
	}

	.order-icon {
		font-size: 50rpx;
		margin-right: 20rpx;
	}

	.order-details {
		display: flex;
		flex-direction: column;
	}

	.order-label {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.8);
		margin-bottom: 8rpx;
	}

	.order-number {
		font-size: 36rpx;
		font-weight: bold;
		color: #ffffff;
	}

	.camera-section {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.section-title {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.title-icon {
		font-size: 36rpx;
		margin-right: 15rpx;
		color: #667eea;
	}

	.title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.camera-options {
		display: flex;
		gap: 20rpx;
	}

	.option-card {
		flex: 1;
		background: #f8f9ff;
		border: 2rpx solid #e0e6ff;
		border-radius: 16rpx;
		padding: 40rpx 20rpx;
		text-align: center;
		transition: all 0.3s ease;
	}

	.option-card:active {
		transform: scale(0.98);
		background: #f0f4ff;
	}

	.option-icon {
		margin-bottom: 20rpx;
	}

	.icon {
		font-size: 60rpx;
	}

	.option-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
		display: block;
	}

	.option-desc {
		font-size: 24rpx;
		color: #666666;
		display: block;
	}

	.preview-section {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.image-preview-list {
		white-space: nowrap;
	}

	.preview-item {
		position: relative;
		display: inline-block;
		width: 150rpx;
		height: 150rpx;
		margin-right: 20rpx;
		border-radius: 12rpx;
		overflow: hidden;
	}

	.preview-image {
		width: 100%;
		height: 100%;
	}

	.remove-btn {
		position: absolute;
		top: -10rpx;
		right: -10rpx;
		width: 40rpx;
		height: 40rpx;
		background: #ff4444;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2rpx 8rpx rgba(255, 68, 68, 0.3);
	}

	.remove-icon {
		font-size: 24rpx;
		color: #ffffff;
		font-weight: bold;
	}

	.upload-section {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.upload-btn {
		width: 100%;
		height: 90rpx;
		background: linear-gradient(135deg, #4CAF50, #45a049);
		border: none;
		border-radius: 12rpx;
		color: #ffffff;
		font-size: 32rpx;
		font-weight: bold;
		transition: all 0.3s ease;
	}

	.upload-btn:not(:disabled):active {
		transform: scale(0.98);
	}

	.upload-btn:disabled {
		opacity: 0.6;
		transform: none;
	}

	.upload-btn.uploading {
		background: #cccccc;
	}

	.upload-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}

	.upload-content {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 60rpx 40rpx;
		margin: 0 40rpx;
		min-width: 500rpx;
	}

	.upload-header {
		text-align: center;
		margin-bottom: 40rpx;
	}

	.upload-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
	}

	.progress-section {
		margin-bottom: 30rpx;
	}

	.progress-bar {
		width: 100%;
		height: 12rpx;
		background: #f0f0f0;
		border-radius: 6rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
	}

	.progress-fill {
		height: 100%;
		background: linear-gradient(135deg, #4CAF50, #45a049);
		transition: width 0.3s ease;
	}

	.progress-text {
		font-size: 28rpx;
		color: #666666;
		text-align: center;
		display: block;
	}

	.upload-status {
		text-align: center;
	}

	.status-text {
		font-size: 26rpx;
		color: #999999;
	}
</style>
